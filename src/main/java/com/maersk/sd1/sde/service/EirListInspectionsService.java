package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.InspectionGate;
import com.maersk.sd1.common.model.InspectionGatePhoto;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.InspectionGatePhotoRepository;
import com.maersk.sd1.common.repository.InspectionGateRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.dto.EirListInspectionsOutputDTO;
import com.maersk.sd1.sde.dto.EirListInspectionsOutputDTO.InspectionData;
import com.maersk.sd1.sde.dto.EirListInspectionsOutputDTO.InspectionPhotoData;

import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class EirListInspectionsService {

    private static final Logger logger = LogManager.getLogger(EirListInspectionsService.class);
    private final InspectionGateRepository inspectionGateRepository;
    private final InspectionGatePhotoRepository inspectionGatePhotoRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    private static final String PRC_GATE_OUT_EMPTY = "PRC_GATE_OUT_EMPTY";

    @Transactional(readOnly = true)
    public EirListInspectionsOutputDTO listInspections(Integer eirId, Integer languageId) {
        logger.info("Starting listInspections with eirId={} and languageId={}", eirId, languageId);

        List<InspectionGate> combined = getCombinedInspectionGates(eirId);
        List<InspectionData> inspectionDataList = buildInspectionDataList(combined, languageId);
        List<InspectionPhotoData> photoDataList = getInspectionPhotoDataList(combined);

        EirListInspectionsOutputDTO output = new EirListInspectionsOutputDTO();
        output.setInspections(inspectionDataList);
        output.setInspectionPhotos(photoDataList);

        logger.info("Finished listInspections with eirId={} and languageId={}", eirId, languageId);
        return output;
    }

    private List<InspectionGate> getCombinedInspectionGates(Integer eirId) {
        List<InspectionGate> firstSet = inspectionGateRepository.findFirstSet(eirId);
        List<InspectionGate> secondSet = inspectionGateRepository.findSecondSet(eirId);
        List<InspectionGate> combined = new ArrayList<>();
        combined.addAll(firstSet);
        combined.addAll(secondSet);
        return combined;
    }

    private List<InspectionData> buildInspectionDataList(List<InspectionGate> combined, Integer languageId) {
        List<InspectionData> inspectionDataList = new ArrayList<>();
        for (InspectionGate ig : combined) {
            InspectionData data = new InspectionData();
            populateInspectionData(data, ig, languageId);
            inspectionDataList.add(data);
        }
        inspectionDataList.sort(Comparator
                .comparing(InspectionData::getEirId)
                .thenComparing(InspectionData::getInspectionDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        return inspectionDataList;
    }

    private void populateInspectionData(InspectionData data, InspectionGate ig, Integer languageId) {
        data.setEirId(ig.getEir().getId());
        data.setInspectionGateId(ig.getId());
        data.setInspectionDate(getInspectionDate(ig));
        populateUserDetails(data, ig);
        data.setInspectionResult(getInspectionResultMessage(ig, languageId));
        data.setAssignmentDate(ig.getAssignmentDate());
        data.setAssignedContainer(getContainerNumber(ig.getOriginalContainer()));
        data.setInspectorObservation(getInspectorObservation(ig));
        populateApproverDetails(data, ig);
        data.setObservationOfChange(ig.getReassignmentObservation() != null ? ig.getReassignmentObservation() : "");
        data.setNewContainer(getContainerNumber(ig.getContainerReassignment()));
        data.setNewAssignmentDate(ig.getDateApprovesOrRejects());
    }

    private LocalDateTime getInspectionDate(InspectionGate ig) {
        if (ig.getInspectionControl() != null && ig.getApprovedInspection() == null) {
            return ig.getInspectionDate();
        } else {
            return ig.getDateApprovesOrRejects();
        }
    }

    private void populateUserDetails(InspectionData data, InspectionGate ig) {
        User mainUser = getMainUser(ig);
        if (mainUser != null) {
            data.setInspectorUserId(mainUser.getId());
            data.setInspectorUserNames(mainUser.getNames());
            data.setInspectorUserLastnames(getUserLastNames(mainUser));
        }
    }

    private User getMainUser(InspectionGate ig) {
        if (ig.getInspectionControl() != null && ig.getApprovedInspection() == null) {
            return ig.getAssignmentUser();
        } else {
            return ig.getApproveRejectUser();
        }
    }

    private String getUserLastNames(User user) {
        String lastName = (user.getFirstLastName() != null ? user.getFirstLastName() : "")
                + (user.getSecondLastName() != null ? (" " + user.getSecondLastName()) : "");
        return lastName.trim();
    }

    private String getInspectionResultMessage(InspectionGate ig, Integer languageId) {
        if (ig.getInspectionControl() != null && ig.getApprovedInspection() == null) {
            return getInspectionControlMessage(ig.getInspectionControl(), languageId);
        } else {
            return getApprovedInspectionMessage(ig.getApprovedInspection(), languageId);
        }
    }

    private String getInspectionControlMessage(Short ctrl, Integer languageId) {
        return switch (ctrl) {
            case 1 -> messageLanguageRepository.fnTranslatedMessage(PRC_GATE_OUT_EMPTY, 2, languageId);
            case 2 -> messageLanguageRepository.fnTranslatedMessage(PRC_GATE_OUT_EMPTY, 4, languageId);
            case 3 -> messageLanguageRepository.fnTranslatedMessage(PRC_GATE_OUT_EMPTY, 5, languageId);
            default -> String.valueOf(ctrl);
        };
    }

    private String getApprovedInspectionMessage(String ap, Integer languageId) {
        return switch (ap) {
            case "0" -> messageLanguageRepository.fnTranslatedMessage("PRC_GATE_OUT_EMPTY_LIGHT", 27, languageId);
            case "1" -> messageLanguageRepository.fnTranslatedMessage(PRC_GATE_OUT_EMPTY, 2, languageId);
            case "2" -> messageLanguageRepository.fnTranslatedMessage("PRC_GATE_OUT_EMPTY_LIGHT", 31, languageId);
            case null, default -> (ap == null) ? "" : ap;
        };
    }

    private String getContainerNumber(Container container) {
        return container != null ? container.getContainerNumber() : "";
    }

    private String getInspectorObservation(InspectionGate ig) {
        if (ig.getInspectionControl() != null && ig.getApprovedInspection() == null) {
            return getObservationForInspectionControl(ig);
        } else {
            return getObservationForApprovedInspection(ig);
        }
    }

    private String getObservationForInspectionControl(InspectionGate ig) {
        String baseObs = ig.getInspectionObservation() == null ? "" : ig.getInspectionObservation();
        String mRechazo = getRejectionReason(ig);
        return baseObs.isEmpty() ? mRechazo : baseObs + ". " + mRechazo;
    }

    private String getObservationForApprovedInspection(InspectionGate ig) {
        String mRechazo = "";
        String inspectorObs = "";

        if (Objects.equals(ig.getApprovedInspection(), "2")) {
            mRechazo = ig.getInspectionObservation() == null ? "" : ig.getInspectionObservation();
        } else {
            inspectorObs = ig.getInspectionObservation() == null ? "" : ig.getInspectionObservation();
        }

        if (!mRechazo.isEmpty()) {
            inspectorObs = "";
        }

        return inspectorObs;
    }

    private String getRejectionReason(InspectionGate ig) {
        if (ig.getCatReassignmentInspComment() != null) {
            String desc = ig.getCatReassignmentInspComment().getDescription() == null ? "" : ig.getCatReassignmentInspComment().getDescription();
            String longD = ig.getCatReassignmentInspComment().getLongDescription();
            if (longD != null && !longD.trim().isEmpty()) {
                return desc + ": " + longD;
            } else {
                return desc;
            }
        }
        return "";
    }

    private void populateApproverDetails(InspectionData data, InspectionGate ig) {
        User userApproverChange = ig.getApproveRejectUser();
        if (userApproverChange != null) {
            data.setApproverUserId(userApproverChange.getId());
            data.setApproverUserNames(userApproverChange.getNames());
            data.setApproverUserLastnames(getUserLastNames(userApproverChange));
        }
    }

    private List<InspectionPhotoData> getInspectionPhotoDataList(List<InspectionGate> combined) {
        List<Integer> gateIds = new ArrayList<>();
        for (InspectionGate ig : combined) {
            gateIds.add(ig.getId());
        }
        List<InspectionPhotoData> photoDataList = new ArrayList<>();
        if (!gateIds.isEmpty()) {
            List<InspectionGatePhoto> photos = inspectionGatePhotoRepository.findPhotosByInspectionGateIds(gateIds);
            for (InspectionGatePhoto photo : photos) {
                InspectionPhotoData pd = getInspectionPhotoData(photo);
                photoDataList.add(pd);
            }
        }
        return photoDataList;
    }

    private static InspectionPhotoData getInspectionPhotoData(InspectionGatePhoto photo) {
        InspectionPhotoData pd = new InspectionPhotoData();
        pd.setEirId(photo.getInspeccionGate().getEir().getId());
        pd.setInspectionGateId(photo.getInspeccionGate().getId());
        pd.setTask(photo.getTask() == null ? "INS" : photo.getTask());
        pd.setInspectionGatePhotoId(photo.getId());
        if (photo.getAttachment() != null) {
            pd.setAttachmentId(photo.getAttachment().getId());
            pd.setAttachmentGuid(photo.getAttachment().getId1());
            pd.setAttachmentUrl(photo.getAttachment().getUrl());
        }
        return pd;
    }
}