package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class CancellationBlockingBookingReleaseObtainInput {
    @Data
    public static class Input {
        @JsonProperty("cancel_bloqueo_booking_id")
        @NotNull(message = "cancel_bloqueo_booking_id cannot be null")
        private Integer cancelBloqueoBookingId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
