package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BuscarCedexMercXCodigoObservableOutput {
    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("cedex_merc_list")
    private List<CedexMercItemOutput> cedexMercList;

    /**
     * Inner class representing a single row from the search results.
     */
    @Data
    public static class CedexMercItemOutput {
        @JsonProperty("cedex_merc_codigo")
        private String cedexMercCodigo;

        @JsonProperty("cedex_merc_descripcion")
        private String cedexMercDescripcion;

        @JsonProperty("merc_hh")
        private Integer mercHH;

        @JsonProperty("merc_costo_material")
        private Integer mercCostoMaterial;

        @JsonProperty("ubicaciones_dano")
        private String ubicacionesDano;

        @JsonProperty("componente_descripcion")
        private String componenteDescripcion;

        @JsonProperty("metodo_descripcion")
        private String metodoDescripcion;

        @JsonProperty("merc_piezas_max")
        private Integer mercPiezasMax;

        @JsonProperty("merc_dimension")
        private String mercDimension;

        @JsonProperty("merc_valor_min")
        private Integer mercValorMin;

        @JsonProperty("merc_valor_max")
        private Integer mercValorMax;

        @JsonProperty("tamano_cnt")
        private String tamanoCnt;

        @JsonProperty("moneda")
        private String moneda;

        @JsonProperty("merc_observacion")
        private String mercObservacion;
    }
}
