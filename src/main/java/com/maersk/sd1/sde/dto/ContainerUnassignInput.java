package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class ContainerUnassignInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        @NotNull
        private BigDecimal unidadNegocioId;

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private BigDecimal subUnidadNegocioId;

        @JsonProperty("lista_preasignacion_contenedor_ids")
        @NotNull
        private String listaPreasignacionContenedorIds;

        @JsonProperty("booking_detalle_id")
        @NotNull
        private Integer bookingDetalleId;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer usuarioId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
