package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoInput;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoOutput;
import com.maersk.sd1.sde.service.BuscarCedexMercXCodigoService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("modulesde/ModuleSDE/module/sde/SDEEMRService")
public class BuscarCedexMercXCodigoController {

    private static final Logger logger = LogManager.getLogger(BuscarCedexMercXCodigoController.class);

    private final BuscarCedexMercXCodigoService buscarCedexMercXCodigoService;

    @Autowired
    public BuscarCedexMercXCodigoController(BuscarCedexMercXCodigoService buscarCedexMercXCodigoService) {
        this.buscarCedexMercXCodigoService = buscarCedexMercXCodigoService;
    }

    /**
     * Controller method that replicates stored procedure logic.
     */
    @PostMapping("/sdebuscarCedexMercXcodigo")
    public ResponseEntity<ResponseController<List<BuscarCedexMercXCodigoOutput>>> buscarCedexMercXCodigo(
            @RequestBody @Valid BuscarCedexMercXCodigoInput.Root request) {
        try {
            BuscarCedexMercXCodigoInput.Input input = request.getPrefix().getInput();
            List<BuscarCedexMercXCodigoOutput> outputList = buscarCedexMercXCodigoService.buscarCedexMercXCodigo(input);
            return ResponseEntity.ok(new ResponseController<>(outputList));
        } catch (Exception e) {
            logger.error("An error occurred while processing buscarCedexMercXCodigo", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>("Internal Error"));
        }
    }
}
