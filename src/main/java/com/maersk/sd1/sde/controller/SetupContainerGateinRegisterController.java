package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterInput;
import com.maersk.sd1.sde.dto.SetupContainerGateinRegisterOutput;
import com.maersk.sd1.sde.service.SetupContainerGateinRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ModuleSDS/module/sds/SDSEdiServiceImp")
public class SetupContainerGateinRegisterController {

    private static final Logger logger = LogManager.getLogger(SetupContainerGateinRegisterController.class.getName());

    private final SetupContainerGateinRegisterService seteoEdiCodecoRegisterService;

    public SetupContainerGateinRegisterController(SetupContainerGateinRegisterService seteoEdiCodecoRegisterService) {
        this.seteoEdiCodecoRegisterService = seteoEdiCodecoRegisterService;
    }

    @PostMapping("/sdeseteoEdiCodecoRegistrar")
    public ResponseEntity<ResponseController<SetupContainerGateinRegisterOutput>> register(@RequestBody @Valid SetupContainerGateinRegisterInput.Root request) {
        try {
            SetupContainerGateinRegisterInput.Input input = request.getPrefix().getInput();
            SetupContainerGateinRegisterOutput output = seteoEdiCodecoRegisterService.registerCodecoSetting(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            SetupContainerGateinRegisterOutput output = new SetupContainerGateinRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

