package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BlManualSearchOutputDTO {

    @JsonProperty("documento_carga_id")
    private Integer cargoDocumentId;

    @JsonProperty("linea_naviera_id")
    private Integer shippingLineId;

    @JsonProperty("programacion_nave_detalle_id")
    private Integer vesselProgrammingDetailId;

    @JsonProperty("nave_nombre")
    private String vesselName;

    @JsonProperty("viaje")
    private String voyage;

    @JsonProperty("operacion")
    private String operation;

    @JsonProperty("puerto_embarque_id")
    private Integer loadingPortId;

    @JsonProperty("puerto_descarga_id")
    private Integer dischargePortId;

    @JsonProperty("empresa_embarcador_id")
    private Integer shipperCompanyId;

    @JsonProperty("empresa_consignatario_id")
    private Integer consigneeCompanyId;

    @JsonProperty("embarcador_detalle")
    private String shipperDetail;

    @JsonProperty("consignatario_detalle")
    private String consigneeDetail;

    @JsonProperty("deposito_vacio_id")
    private Integer emptyDepotId;

    @JsonProperty("shipping_line_name")
    private String shippingLineName;

    @JsonProperty("port_loading_name")
    private String loadingPortName;

    @JsonProperty("port_discharge_name")
    private String dischargePortName;

    @JsonProperty("company_shipper_name")
    private String shipperCompanyName;

    @JsonProperty("company_consignee_name")
    private String consigneeCompanyName;

    @JsonProperty("deposit_empty_name")
    private String emptyDepotName;

    @JsonProperty("move_type_id")
    private Integer moveTypeId;
}
