package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.DepotRegisterInput;
import com.maersk.sd1.sds.dto.DepotRegisterOutput;
import com.maersk.sd1.sds.service.DepotRegisterService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSDepositoServiceImp")
public class DepotRegisterController {

    private static final Logger logger = LogManager.getLogger(DepotRegisterController.class);

    private final DepotRegisterService depotRegisterService;

    @Autowired
    public DepotRegisterController(DepotRegisterService depotRegisterService) {
        this.depotRegisterService = depotRegisterService;
    }

    @PostMapping("/sdsdepositoRegistrar")
    public ResponseEntity<ResponseController<DepotRegisterOutput>> registerDepot(
            @RequestBody @Valid DepotRegisterInput.Root request) {
        try {

            if (request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>(new DepotRegisterOutput()));
            }

            DepotRegisterInput.Input input = request.getPrefix().getInput();
            DepotRegisterOutput output = depotRegisterService.registerDepot(input);

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            DepotRegisterOutput output = new DepotRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
