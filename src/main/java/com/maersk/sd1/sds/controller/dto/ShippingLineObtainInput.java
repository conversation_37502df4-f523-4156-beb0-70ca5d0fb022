package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ShippingLineObtainInput {

    @Data
    public static class Input {
        @JsonProperty("linea_naviera_id")
        @NotNull
        private Integer shippingLineId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }

    private ShippingLineObtainInput() {
        // Private constructor to hide the implicit public one
    }
}