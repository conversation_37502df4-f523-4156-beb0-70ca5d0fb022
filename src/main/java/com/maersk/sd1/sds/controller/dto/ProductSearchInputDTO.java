package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ProductSearchInputDTO {

    @Data
    public static class Input {
        @JsonProperty("languageId")
        private Integer languageId;

        @JsonProperty("productId")
        private Integer productId;

        @JsonProperty("productName")
        private String productName;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ProductSearchInputDTO.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ProductSearchInputDTO.Prefix prefix;
    }
}