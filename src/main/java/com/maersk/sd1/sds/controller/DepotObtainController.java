package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.DepotObtainInput;
import com.maersk.sd1.sds.controller.dto.DepotObtainOutput;
import com.maersk.sd1.sds.service.DepotObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSDepositoServiceImp")
public class DepotObtainController {

    private static final Logger logger = LogManager.getLogger(DepotObtainController.class.getName());

    private final DepotObtainService depotObtainService;

    public DepotObtainController(DepotObtainService depotObtainService) {
        this.depotObtainService = depotObtainService;
    }

    @PostMapping("/sdsdepositoObtener")
    public ResponseEntity<ResponseController<DepotObtainOutput>> obtainDepot(@RequestBody @Valid DepotObtainInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            Integer depotId = request.getPrefix().getInput().getDepotId();
            if(depotId == null){
                return ResponseEntity.status(400).body(new ResponseController<>("depotId cannot be null."));
            }
            logger.info("Received request to obtain depot with ID: {}", depotId);

            DepotObtainOutput output = depotObtainService.getDepotById(depotId);
            if (output == null) {
                logger.warn("Depot not found for ID: {}", depotId);
                String message = "Depot not found for ID: " + depotId;
                return ResponseEntity.ok(new ResponseController<>(message));
            }

            logger.info("Successfully retrieved Depot data for ID: {}", depotId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing obtainDepot request.", e);
            String message = "An error occurred while processing obtainDepot request." + e.toString();
            return ResponseEntity.status(500).body(new ResponseController<>(message));
        }
    }
}