package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.ShipListInputDTO;
import com.maersk.sd1.sds.dto.ShipListOutputDTO;
import com.maersk.sd1.common.repository.ShipListRepository;
import com.maersk.sd1.sds.dto.ShipListRowDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ShipListService {

    private static final Logger logger = LogManager.getLogger(ShipListService.class);

    private final ShipListRepository navesListRepository;

    @Autowired
    public ShipListService(ShipListRepository navesListRepository) {
        this.navesListRepository = navesListRepository;
    }

    @Transactional(readOnly = true)
    public ShipListOutputDTO getNavesList(ShipListInputDTO.Input input) {
        ShipListOutputDTO response = new ShipListOutputDTO();
        response.setRespEstado(1); // success by default
        try {
            // Null checks
            if (input.getPage() == null || input.getSize() == null) {
                throw new IllegalArgumentException("Page and Size must not be null");
            }

            // Prepare paging sorted by id desc
            PageRequest pageRequest = PageRequest.of(
                    input.getPage() - 1,
                    input.getSize(),
                    Sort.by(Sort.Direction.DESC, "id")
            );

            // Retrieve paginated data
            Page<ShipListRowDTO> resultPage = navesListRepository.findByFilters(
                    input.getNaveId(),
                    input.getNave(),
                    input.getCallSign(),
                    input.getImoNumber(),
                    input.getActive(),
                    input.getName(),
                    pageRequest
            );

            // Set the total records
            response.setTotalRecords(resultPage.getTotalElements());

            // Set the list of data
            response.setData(resultPage.getContent());

            // We do not implement the fn_HoraLocal(@unidad_negocio_id, date) here explicitly.
            // In a real scenario, we would adjust dates as needed.

            response.setRespMensaje("Success");
        } catch (Exception e) {
            logger.error("Error fetching vessel list", e);
            response.setRespEstado(0);
            response.setRespMensaje(e.getMessage());
        }
        return response;
    }
}

