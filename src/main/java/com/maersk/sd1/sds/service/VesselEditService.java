package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.sds.dto.VesselEditInput;
import com.maersk.sd1.sds.dto.VesselEditOutput;
import com.maersk.sd1.common.model.Vessel;
import com.maersk.sd1.common.repository.VesselRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class VesselEditService {

    private static final Logger logger = LogManager.getLogger(VesselEditService.class);
    private static final String INS_NAVE = "INS_NAVE";

    private final VesselRepository vesselRepository;
    private final UserRepository userRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public VesselEditService(VesselRepository vesselRepository,UserRepository userRepository, MessageLanguageRepository messageLanguageRepository) {
        this.vesselRepository = vesselRepository;
        this.userRepository = userRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public VesselEditOutput editVessel(VesselEditInput.Input input) {
        VesselEditOutput output = new VesselEditOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            Vessel existingVessel = findExistingVessel(input.getNaveId(), input.getLanguageId(), output);
            if (existingVessel == null) {
                return output;
            }

            if (checkForDuplicates(input, existingVessel, output)) {
                return output;
            }

            updateVessel(existingVessel, input);
            vesselRepository.save(existingVessel);

            String successMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, input.getLanguageId());
            output.setRespEstado(1);
            output.setRespMensaje(successMsg);

        } catch (Exception e) {
            logger.error("Exception while editing vessel.", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }

    private Vessel findExistingVessel(Integer naveId, Integer languageId, VesselEditOutput output) {
        Vessel existingVessel = vesselRepository.findById(naveId).orElse(null);
        if (existingVessel == null) {
            logger.warn("Vessel with id {} not found.", naveId);
            String notFoundMsg = messageLanguageRepository.fnTranslatedMessage(INS_NAVE, 3, languageId);
            output.setRespEstado(2);
            output.setRespMensaje(String.format(notFoundMsg, naveId));
        }
        return existingVessel;
    }

    private boolean checkForDuplicates(VesselEditInput.Input input, Vessel existingVessel, VesselEditOutput output) {
        boolean changedShip = !input.getShip().equalsIgnoreCase(existingVessel.getShip());
        boolean changedName = !input.getName().equalsIgnoreCase(existingVessel.getName());

        if (changedShip && vesselRepository.findFirstByShipIgnoreCaseAndIdNot(input.getShip(), input.getNaveId()).isPresent()) {
            String msgCode = messageLanguageRepository.fnTranslatedMessage(INS_NAVE, 1, input.getLanguageId());
            output.setRespMensaje(String.format(msgCode, input.getNaveId()));
            output.setRespEstado(2);
            return true;
        }

        if (changedName && vesselRepository.findFirstByNameIgnoreCaseAndIdNot(input.getName(), input.getNaveId()).isPresent()) {
            String msgCode = messageLanguageRepository.fnTranslatedMessage(INS_NAVE, 2, input.getLanguageId());
            output.setRespMensaje(String.format(msgCode, input.getNaveId()));
            output.setRespEstado(2);
            return true;
        }

        return false;
    }

    private void updateVessel(Vessel existingVessel, VesselEditInput.Input input) {
        existingVessel.setShip(input.getShip());
        existingVessel.setCallSign(input.getCallSign());
        existingVessel.setImoNumber(input.getImoNumber());
        existingVessel.setActive(input.getActive());
        existingVessel.setName(input.getName());
        if (input.getUserModificationId() != null) {
            Optional<User> userOpt = userRepository.findById(input.getUserModificationId());
            userOpt.ifPresent(existingVessel::setModificationUser);
        }
        existingVessel.setModificationDate(java.time.LocalDateTime.now());
    }
}