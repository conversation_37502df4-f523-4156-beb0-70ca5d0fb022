package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.BookingDetailsDTO;
import com.maersk.sd1.sds.dto.BookingEditFileInputDTO;
import com.maersk.sd1.sds.dto.BookingEditFileOutputDTO;
import com.maersk.sd1.common.repository.BookingEdiFileRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.maersk.sd1.sds.method.MessageFunctionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@Service
public class BookingEditFileService {

    private static final Logger logger = LoggerFactory.getLogger(BookingEditFileService.class);

    private final BookingEdiFileRepository bookingEdiFileRepository;
    private final BookingRepository bookingRepository;
    private final MessageFunctionService messageFunctionService;

    @Autowired
    public BookingEditFileService(BookingEdiFileRepository bookingEdiFileRepository, BookingRepository bookingRepository, MessageFunctionService messageFunctionService) {
        this.bookingEdiFileRepository = bookingEdiFileRepository;
        this.bookingRepository = bookingRepository;
        this.messageFunctionService = messageFunctionService;
    }

    @Transactional
    public BookingEditFileOutputDTO.Root editBooking(BookingEditFileInputDTO.Input input) {

        Integer bookingId = input.getBookingId();
        String bookingNumber = input.getBookingNumber();
        String bookingIssueDate = input.getBookingIssueDate();
        Integer vesselProgrammingDetailId = input.getVesselProgrammingDetailId();
        Integer loadingPortId = input.getLoadingPortId();
        Integer dischargePortId = input.getDischargePortId();
        Integer destinationPortId = input.getDestinationPortId();
        Integer shippingLineId = input.getShippingLineId();
        Integer clientCompanyId = input.getClientCompanyId();
        Integer shipperCompanyId = input.getShipperCompanyId();
        Integer emptyDepotId = input.getEmptyDepotId();
        Integer fullDepotId = input.getFullDepotId();
        Integer productId = input.getProductId();
        String temperatureC = input.getTemperatureC();
        Integer imoId = input.getImoId();
        String cargo = input.getCommodity();
        Integer modificationUserId = input.getModificationUserId();
        Integer subBusinessUnitId = input.getSubBusinessUnitId();
        Integer languageId = input.getLanguageId();
        Integer moveTypeCategoryId = input.getMovementTypeId();
        Boolean maerskDepotWithSd1 = input.getMaerskDepotWithSd1();
        Integer originDestinationDepotId = input.getOriginDestinationDepotId();

        Integer previousVesselProgrammingDetailId = null;
        String previousBookingNumber = null;
        Integer approvedBooking = null;
        Boolean dangerousCargo = false;
        boolean updateBooking = true;

        Integer responseStatus = 0;
        String responseMessage = "";

        try {
            List<BookingDetailsDTO> bookingDetails = bookingRepository.findBookingDetails(bookingId);
            if (imoId != null && imoId > 0) {
                dangerousCargo = true;
            }
            if (!bookingDetails.isEmpty()) {
                BookingDetailsDTO details = bookingDetails.get(0);
                previousBookingNumber = details.getBookingNumber();
                previousVesselProgrammingDetailId = details.getVesselProgrammingDetailId();
                approvedBooking = (details.getApprovedBooking() != null && details.getApprovedBooking()) ? 1 : 0;
            }
            if ((previousBookingNumber != null && !previousBookingNumber.equalsIgnoreCase(bookingNumber)) ||
                    (previousVesselProgrammingDetailId != null && !previousVesselProgrammingDetailId.equals(vesselProgrammingDetailId))) {
                int sum = bookingEdiFileRepository.findTotalAssign(bookingId);
                if(sum > 0){
                    updateBooking = false;
                    responseStatus = 2;
                    responseMessage = messageFunctionService.getTranslatedMessage("UPDATE_BOOKING_DETAIL", 1, languageId);
                }
            }
            if (responseStatus == 0) {
                if ((previousVesselProgrammingDetailId != null && !previousVesselProgrammingDetailId.equals(vesselProgrammingDetailId)) ||
                        (previousBookingNumber != null && !previousBookingNumber.equalsIgnoreCase(bookingNumber))) {
                    if(bookingEdiFileRepository.checkDuplicateBooking(vesselProgrammingDetailId, bookingNumber).isPresent()){
                        updateBooking = false;
                        responseStatus = 2;
                        if(previousBookingNumber != null && !previousBookingNumber.equalsIgnoreCase(bookingNumber)){
                            responseMessage = messageFunctionService.getTranslatedMessage("INSERT_BOOKING", 3, languageId);
                        }
                        if(previousVesselProgrammingDetailId != null && !previousVesselProgrammingDetailId.equals(vesselProgrammingDetailId)){
                            responseMessage = messageFunctionService.getTranslatedMessage("INSERT_BOOKING", 1, languageId);
                        }
                    }
                }
                if(updateBooking) {
                    bookingEdiFileRepository.updateBooking(bookingId, bookingNumber, bookingIssueDate, vesselProgrammingDetailId, loadingPortId, dischargePortId, destinationPortId, shippingLineId, clientCompanyId, shipperCompanyId, emptyDepotId, fullDepotId, productId, temperatureC, imoId, cargo, modificationUserId, subBusinessUnitId, moveTypeCategoryId, maerskDepotWithSd1, originDestinationDepotId);
                    if(approvedBooking != null && approvedBooking == 1) {
                        int cargoDocumentId = 0;
                        cargoDocumentId = bookingEdiFileRepository.findDocumentCargoId(bookingNumber, vesselProgrammingDetailId);
                        if (cargoDocumentId > 0) {
                            bookingEdiFileRepository.updateCargoDocument(vesselProgrammingDetailId, bookingNumber, loadingPortId, dischargePortId, destinationPortId, shippingLineId, fullDepotId, shipperCompanyId, clientCompanyId, emptyDepotId, modificationUserId, moveTypeCategoryId, maerskDepotWithSd1, originDestinationDepotId, cargoDocumentId);
                            bookingEdiFileRepository.updateDocumentCargoDetail(productId, dangerousCargo, cargo, modificationUserId, cargoDocumentId);
                        }
                    }
                    responseStatus = 1;
                    responseMessage = messageFunctionService.getTranslatedMessage("GENERAL", 10, languageId);
                }
            }

        } catch (Exception e) {
            logger.error("An error occurred while updating the booking.", e);
            responseMessage = "An error occurred while updating the booking.";
        }

        BookingEditFileOutputDTO.Root output = new BookingEditFileOutputDTO.Root();
        BookingEditFileOutputDTO.Output outputData = output.getPrefix().getOutput();

        outputData.setResponseStatus(responseStatus);
        outputData.setResponseMessage(responseMessage);

        return output;
    }
}