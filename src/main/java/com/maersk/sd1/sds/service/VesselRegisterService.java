package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Vessel;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.repository.VesselRepository;
import com.maersk.sd1.sds.dto.VesselRegisterOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class VesselRegisterService {

    private static final Logger logger = LogManager.getLogger(VesselRegisterService.class);

    private final VesselRepository vesselRepository;
    private final UserRepository userRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public VesselRegisterService(
            VesselRepository vesselRepository,
            UserRepository userRepository,
            MessageLanguageRepository messageLanguageRepository
    ) {
        this.vesselRepository = vesselRepository;
        this.userRepository = userRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public VesselRegisterOutput registerVessel(
            String ship,
            String callSign,
            String imoNumber,
            Boolean active,
            Integer userRegistrationId,
            String name,
            Integer languageId
    ) {
        VesselRegisterOutput output = new VesselRegisterOutput();
        output.setRespNewId(0);
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            Vessel vesselWithSameShip = vesselRepository.findFirstByShipIgnoreCase(ship).orElse(null);
            Vessel vesselWithSameName = vesselRepository.findFirstByNameIgnoreCase(name).orElse(null);

            StringBuilder validationMsg = new StringBuilder();
            boolean isDuplicate = false;

            if (vesselWithSameShip != null) {
                String msgCode = messageLanguageRepository.fnTranslatedMessage("INS_NAVE", 1, languageId);
                msgCode = msgCode.replace("{IDX}", String.valueOf(vesselWithSameShip.getId()));
                validationMsg.append(msgCode);
                isDuplicate = true;
            }

            if (vesselWithSameName != null) {
                String msgCode = messageLanguageRepository.fnTranslatedMessage("INS_NAVE", 2, languageId);
                msgCode = msgCode.replace("{IDX}", String.valueOf(vesselWithSameName.getId()));
                if (!validationMsg.isEmpty()) {
                    validationMsg.append(" ");
                }
                validationMsg.append(msgCode);
                isDuplicate = true;
            }

            if (isDuplicate) {
                output.setRespNewId(0);
                output.setRespEstado(2);
                output.setRespMensaje(validationMsg.toString());
            } else {
                User registrationUser = userRepository.findById(userRegistrationId)
                        .orElseThrow(() -> new IllegalArgumentException("User not found with id: " + userRegistrationId));

                Vessel newVessel = new Vessel();
                newVessel.setShip(ship);
                newVessel.setCallSign(callSign);
                newVessel.setImoNumber(imoNumber);
                newVessel.setActive(active);
                newVessel.setRegistrationUser(registrationUser);
                newVessel.setName(name);

                newVessel.setRegistrationDate(LocalDateTime.now());

                vesselRepository.save(newVessel);

                output.setRespNewId(newVessel.getId());
                output.setRespEstado(1);
                String successMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, languageId);
                output.setRespMensaje(successMessage);
            }
        } catch (Exception e) {
            logger.error("Error while registering the vessel.", e);
            output.setRespEstado(0);
            output.setRespNewId(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
