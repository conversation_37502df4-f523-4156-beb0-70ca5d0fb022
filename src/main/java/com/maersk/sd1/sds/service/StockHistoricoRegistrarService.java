package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.HistoricalInfo;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.HistoricalInfoRepository;
import com.maersk.sd1.sds.dto.StockHistoricoRegistrarOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class StockHistoricoRegistrarService {

    private static final Logger logger = LogManager.getLogger(StockHistoricoRegistrarService.class);

    private static final Integer COLOMBIA_PARENT_ID = 31;
    private static final Integer ECUADOR_PARENT_ID = 2;

    private final BusinessUnitRepository businessUnitRepository;
    private final HistoricalInfoRepository historicalInfoRepository;
    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public StockHistoricoRegistrarService(BusinessUnitRepository businessUnitRepository,
                                          HistoricalInfoRepository historicalInfoRepository,
                                          JdbcTemplate jdbcTemplate) {
        this.businessUnitRepository = businessUnitRepository;
        this.historicalInfoRepository = historicalInfoRepository;
        this.jdbcTemplate = jdbcTemplate;
    }

    @Transactional
    public StockHistoricoRegistrarOutput registerStockHistorico(Integer usuarioRegistroId) {
        StockHistoricoRegistrarOutput output = new StockHistoricoRegistrarOutput();
        try {
            // 1) Get subunits of Colombia
            List<BusinessUnit> colombiaSubUnits = businessUnitRepository.findActiveSubUnitsByParent(COLOMBIA_PARENT_ID);

            // 2) Get subunits of Ecuador
            List<BusinessUnit> ecuadorSubUnits = businessUnitRepository.findActiveSubUnitsByParent(ECUADOR_PARENT_ID);

            // 3) All subunits merged
            List<SubUnitRecord> subUnitRecords = new ArrayList<>();
            for (BusinessUnit subUnit : colombiaSubUnits) {
                subUnitRecords.add(new SubUnitRecord(COLOMBIA_PARENT_ID, subUnit.getId()));
            }
            for (BusinessUnit subUnit : ecuadorSubUnits) {
                subUnitRecords.add(new SubUnitRecord(ECUADOR_PARENT_ID, subUnit.getId()));
            }

            // 4) Loop each record, call logic to retrieve JSON detail, and insert a new HistoricalInfo record
            for (SubUnitRecord r : subUnitRecords) {
                String detalleJSON = getStockEmptyDetalle(r.subUnidadNegocioId);
                if (detalleJSON != null && !detalleJSON.isEmpty()) {
                    // Insert into HistoricalInfo
                    HistoricalInfo historicalInfo = new HistoricalInfo();

                    // Set catalog type ID
                    Catalog catalog = new Catalog();
                    catalog.setId(48372);
                    historicalInfo.setCatHistoricalInfoType(catalog);

                    // Set business unit and subunit IDs
                    BusinessUnit parentUnit = new BusinessUnit();
                    parentUnit.setId(r.unidadNegocioId);
                    historicalInfo.setBusinessUnit(parentUnit);

                    BusinessUnit subUnit = new BusinessUnit();
                    subUnit.setId(r.subUnidadNegocioId);
                    historicalInfo.setSubBusinessUnit(subUnit);

                    // Set the JSON detail
                    historicalInfo.setHistoricalInfoDetail(detalleJSON);

                    // Set user ID for registration (using default value of 1 if null)
                    User registrationUser = new User();
                    registrationUser.setId(usuarioRegistroId != null ? usuarioRegistroId : 1);
                    historicalInfo.setRegistrationUser(registrationUser);

                    // Set record as active
                    historicalInfo.setActive('1');

                    // Set registration date to current date
                    historicalInfo.setRegistrationDate(LocalDateTime.now());


                    // Save the historical info record
                    historicalInfoRepository.save(historicalInfo);
                }
            }

            output.setRespEstado(1);
            output.setRespMensaje("Procesado Correctamente");
            return output;
        } catch (Exception e) {
            logger.error("Error in registerStockHistorico", e);
            output.setRespEstado(2);
            output.setRespMensaje("Ocurrió un error: " + e.getMessage());
            return output;
        }
    }

    /**
     * Executes the stored procedure to get stock details for the subunit.
     * Uses JdbcTemplate to execute the stored procedure and retrieve the JSON result.
     */
    private String getStockEmptyDetalle(Integer subUnidadNegocioId) {
        String storedProcedureCall = String.format(
                "EXEC sde.stock_empty_detalle @sub_unidad_negocio_id = %d, @detalleJSON OUTPUT;", subUnidadNegocioId);

        try {
            // Execute the stored procedure using JdbcTemplate
            List<Map<String, Object>> result = jdbcTemplate.queryForList(storedProcedureCall);

            // Assuming the stored procedure returns a result with the JSON as the first column
            if (!result.isEmpty()) {
                return result.get(0).get("detalleJSON").toString();  // Adjust this if your output differs
            }
        } catch (Exception e) {
            logger.error("Error executing stored procedure for subUnidadNegocioId: {}", subUnidadNegocioId, e);
        }

        return null;  // Return null if no result or an error occurs
    }

    /**
     * Helper class to store subunit pair: (unidad_negocio_id, sub_unidad_negocio_id)
     */
    private record SubUnitRecord(Integer unidadNegocioId, Integer subUnidadNegocioId) {
    }
}


