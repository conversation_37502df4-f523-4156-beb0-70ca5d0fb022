package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class VesselProgrammingByIdOutput {

    @JsonProperty("vessel_programming_id")
    private Integer vesselProgrammingId;

    @JsonProperty("vessel_id")
    private Integer vesselId;

    @JsonProperty("vessel_name")
    private String vesselName;

    @JsonProperty("voyage")
    private String voyage;

    @JsonProperty("eta_date")
    private LocalDate etaDate;

    @JsonProperty("etd_date")
    private LocalDate etdDate;

    @JsonProperty("shipping_agency_company_id")
    private Integer shippingAgencyCompanyId;

    @JsonProperty("shipping_agency_legal_name")
    private String shippingAgencyLegalName;

    @JsonProperty("ope_port_company_id")
    private Integer opePortCompanyId;

    @JsonProperty("ope_port_legal_name")
    private String opePortLegalName;

    @JsonProperty("active")
    private Boolean active;
}