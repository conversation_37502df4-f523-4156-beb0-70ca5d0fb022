package com.maersk.sd1.sds.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class ServiceCopyListPendingProcessOutput {

    private Integer ediCoparnSetupId;
    private Integer shippingLineId;
    private String shippingLine;
    private Integer ediCoparnId;
    private String ediCoparnOriginalFileName;
    private Integer order;
    private String bookingNumber;
    private String ediCoparnCreationDate;
    private LocalDateTime registrationDate;
    private String ediCoparnReservationStatus;

}
