package com.maersk.sd1.sds.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NewDODetail {

    private Integer bookingId;
    private Integer bookingDetailId;
    private Integer doItem;
    private Integer catContainerType;
    private Integer catSize;
    private Boolean refrigeratedCargo;
}
