package com.maersk.sd1.sdf.dto;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@Builder
public class IncomeProgrammingGetInputDTO {
    @Data
    public static class Input {
        @JsonProperty("transport_planning_id")
        private Integer transportPlanningId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private IncomeProgrammingGetInputDTO.Input input;
    }
    @Data
    public static class Root {
        @JsonProperty("SDF")
        private IncomeProgrammingGetInputDTO.Prefix prefix;
    }
}
