package com.maersk.sd1.sdf.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Data
@UtilityClass
public class DocumentationFullListInput {

    @Data
    public static class Input {

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("business_unit_id")
        @NotNull
        private Long businessUnitId;

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Long subBusinessUnitId;

        @JsonProperty("status_id")
        private Integer statusId;

        @JsonProperty("eta_min")
        private LocalDateTime etaMin;

        @JsonProperty("eta_max")
        private LocalDateTime etaMax;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

        @JsonProperty("shipper_name")
        private String shipperName;

        @JsonProperty("consignee_name")
        private String consigneeName;

        @JsonProperty("document_number")
        private String documentNumber;

        @JsonProperty("movement_type")
        private Long movementType;

        @JsonProperty("containers")
        private List<String> containers;

        @JsonProperty("page")
        @NotNull
        @Positive
        private Integer page;

        @JsonProperty("size")
        @NotNull
        @Positive
        private Integer size;

        @JsonSetter("eta_min")
        public void setEtaMin(String etaMin) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            this.etaMin = LocalDate.parse(etaMin, formatter).atStartOfDay();
        }

        @JsonSetter("eta_max")
        public void setEtaMax(String etaMax) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
            this.etaMax = LocalDate.parse(etaMax, formatter).atStartOfDay();
        }

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDF")
        @NotNull
        private Prefix prefix;
    }
}
