package com.maersk.sd1.sdf.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdf.controller.dto.DocumentationFullListInput;
import com.maersk.sd1.sdf.controller.dto.DocumentationFullListOutput;
import com.maersk.sd1.sdf.service.DocumentationFullListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDF/module/sdf/SDFIncomeProgrammingServiceImp")
public class DocumentationFullListController {

    private static final Logger logger = LogManager.getLogger(DocumentationFullListController.class);

    private final DocumentationFullListService documentationFullListService;

    @PostMapping("/sdfdocumentationFullList")
    public ResponseEntity<ResponseController<DocumentationFullListOutput>> getDocumentationFullList(@RequestBody @Valid DocumentationFullListInput.Root request) {
        try {
            logger.info("Incoming request for getDocumentationFullList");
            DocumentationFullListInput.Input input = request.getPrefix().getInput();
            DocumentationFullListOutput result = documentationFullListService.getDocumentationFullList(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            DocumentationFullListOutput errorOutput = new DocumentationFullListOutput();
            errorOutput.setTotalRecords(0L);
            errorOutput.setData(null);
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}