package com.maersk.sd1.sdy.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class PlanningLocationContainerQuantityRemovedUpdateInput {

    @NotNull
    private List<LocationData> locationDataList;

    @NotNull
    private String userAlias;

    @Data
    public static class LocationData{
        private Integer locationContainerId;
        private Integer quantityRemoved;
    }
}
