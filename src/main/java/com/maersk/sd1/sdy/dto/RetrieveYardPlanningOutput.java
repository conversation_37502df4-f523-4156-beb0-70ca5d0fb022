package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RetrieveYardPlanningOutput {

    private Integer id;
    private Integer layoutId;
    private String code;
    private String name;
    private Integer zoom;
    private BigDecimal latitude;
    private String color;
    private BigDecimal longitude;
    private String configuration;
    private Boolean active;
    private Integer registrationUserId;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime registrationDate;

    private Integer modificationUserId;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime modificationDate;

    private Integer businessUnitId;
    private Integer parentBusinessUnitId;

    // Custom LocalDateTime serializer to format to "yyyy-MM-dd HH:mm:ss"
    public static class LocalDateTimeSerializer extends StdSerializer<LocalDateTime> {

        private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        public LocalDateTimeSerializer() {
            super(LocalDateTime.class);
        }

        @Override
        public void serialize(LocalDateTime value, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeString(value.format(formatter));
        }
    }
}
