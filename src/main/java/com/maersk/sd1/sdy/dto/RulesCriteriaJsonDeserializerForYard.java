package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RulesCriteriaJsonDeserializerForYard extends JsonDeserializer<List<GetRulesByCriteriaInYardInput.Criteria>> {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<GetRulesByCriteriaInYardInput.Criteria> deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {
        String escapedJson = p.getValueAsString();
        if (escapedJson == null) {
            return null;
        }
        // Unescape the JSON string
        String unescapedJson = escapedJson.replace("\\\"", "\"")
                                          .replace("\\n", "")
                                          .replace("\\r", "");
        // Remove leading/trailing quotes if present
        if (unescapedJson.startsWith("\"") && unescapedJson.endsWith("\"")) {
            unescapedJson = unescapedJson.substring(1, unescapedJson.length() - 1);
        }
        List<GetRulesByCriteriaInYardInput.Criteria> result = new ArrayList<>();
        JsonNode root = objectMapper.readTree(unescapedJson);
        for (JsonNode node : root) {
            GetRulesByCriteriaInYardInput.Criteria criteria = objectMapper.treeToValue(node, GetRulesByCriteriaInYardInput.Criteria.class);
            JsonNode contenedor = node.get("contenedor");
            if (contenedor != null && contenedor.has("clase")) {
                JsonNode clase = contenedor.get("clase");
                if (clase != null && clase.has("id")) {
                    String classId = clase.get("id").asText();
                    try {
                        criteria.setCatClassId(classId != null ? Integer.valueOf(classId) : null);
                    } catch (NumberFormatException e) {
                        criteria.setCatClassId(null);
                    }
                }
            }
            result.add(criteria);
        }
        return result;
    }
}
