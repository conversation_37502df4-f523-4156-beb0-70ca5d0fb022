package com.maersk.sd1.sdy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LocationInfo {
    private Integer locationId;
    private Integer blockId;
    private Integer cellId;
    private Integer levelId;
    
    // For 40ft containers that occupy two positions
    private Integer secondLocationId;
    private Integer secondBlockId;
    private Integer secondCellId;
    private Integer secondLevelId;
}
