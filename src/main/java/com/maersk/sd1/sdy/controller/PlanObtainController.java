package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.controller.dto.PlanObtainInput;
import com.maersk.sd1.sdy.controller.dto.PlanObtainOutput;
import com.maersk.sd1.sdy.service.PlanObtainService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYPlanoServiceImp")
@RequiredArgsConstructor
public class PlanObtainController {

    private final PlanObtainService planObtainService;

    @PostMapping("/sdyplanoObtener")
    public ResponseEntity<ResponseController<PlanObtainOutput>> obtenerPlano(
            @RequestBody PlanObtainInput.Root request) {
        if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
        }
        Integer planoId = request.getPrefix().getInput().getPlanoId();
        if(planoId == null) {
            return ResponseEntity.badRequest().body(new ResponseController<>("Plano ID is required."));
        }
        try {
            PlanObtainOutput response = planObtainService.obtainPlan(planoId);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(new ResponseController<>(e.getMessage()));
        }
    }
}