package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.WorkPointDeleteInput;
import com.maersk.sd1.sdy.dto.WorkPointDeleteOutput;
import com.maersk.sd1.sdy.service.WorkPointDeleteService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYPuntoTrabajoServiceImp")
public class WorkPointDeleteController {

    private static final Logger logger = LogManager.getLogger(WorkPointDeleteController.class);

    private final WorkPointDeleteService workPointDeleteService;

    @PostMapping("/sdypuntoTrabajoEliminar")
    public ResponseEntity<ResponseController<WorkPointDeleteOutput>> deleteWorkPoint(@RequestBody @Valid WorkPointDeleteInput.Root request) {
        try {
            logger.info("Request received: {}", request);
            WorkPointDeleteInput.Input input = request.getPrefix().getInput();
            WorkPointDeleteOutput output = workPointDeleteService.deleteWorkPoint(
                    input.getWorkPointId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            WorkPointDeleteOutput output = new WorkPointDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
