package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.ContainerLocationRepository;
import com.maersk.sd1.sdy.dto.PlanningLocationContainerQuantityRemovedUpdateInput;
import com.maersk.sd1.sdy.dto.PlanningLocationContainerQuantityRemovedUpdateOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class PlanningLocationContainerQuantityRemovedUpdateService {

    private static final Logger logger = LogManager.getLogger(PlanningLocationContainerQuantityRemovedUpdateService.class);

    private final ContainerLocationRepository containerLocationRepository;

    @Transactional
    public PlanningLocationContainerQuantityRemovedUpdateOutput updateQuantityRemovedToEquipmentLocation(
            PlanningLocationContainerQuantityRemovedUpdateInput input) {

        PlanningLocationContainerQuantityRemovedUpdateOutput output = new PlanningLocationContainerQuantityRemovedUpdateOutput();

        try{
            logger.info("Starting updateQuantityRemovedToEquipmentLocation with input: {}", input);

            List<PlanningLocationContainerQuantityRemovedUpdateInput.LocationData> locations= input.getLocationDataList();

            for(PlanningLocationContainerQuantityRemovedUpdateInput.LocationData location : locations){

                containerLocationRepository.updateQuantityRemovedForLocation(location.getQuantityRemoved(), location.getLocationContainerId());

            }
            output.setRespEstado(true);
            output.setRespMensaje("Container locations updated successfully");
        } catch (Exception e) {
            logger.error("Error updating container locations", e);
            output.setRespEstado(false);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
