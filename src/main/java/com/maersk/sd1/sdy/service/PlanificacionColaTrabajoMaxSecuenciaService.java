package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.PlanificacionColaTrabajoMaxSecuenciaOutput;
import com.maersk.sd1.common.repository.PlanificacionColaTrabajoMaxSecuenciaRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PlanificacionColaTrabajoMaxSecuenciaService {

    private static final Logger logger = LogManager.getLogger(PlanificacionColaTrabajoMaxSecuenciaService.class);

    @Autowired
    private PlanificacionColaTrabajoMaxSecuenciaRepository planificacionColaTrabajoMaxSecuenciaRepository;

    @Transactional(readOnly = true)
    public PlanificacionColaTrabajoMaxSecuenciaOutput getMaxSecuencia(Integer colaTrabajo) {
        PlanificacionColaTrabajoMaxSecuenciaOutput output = new PlanificacionColaTrabajoMaxSecuenciaOutput();
        try {
            Integer maxValue = planificacionColaTrabajoMaxSecuenciaRepository.findMaxSequenceByWorkQueueId(colaTrabajo);
            if (maxValue == null) {
                maxValue = 0;
            }
            int nextValue = maxValue + 1;
            output.setValorMaximo(nextValue);
            output.setRespEstado(1);
            output.setRespMensaje("Successfully retrieved next sequence value.");
        } catch (Exception e) {
            logger.error("Error while fetching max sequence: ", e);
            // The above line should be output.setValorMaximo(null), correcting below:
            output.setValorMaximo(null);
            output.setRespEstado(0);
            output.setRespMensaje("Error: " + e.getMessage());
        }
        return output;
    }
}
