package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.GroupCode;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.repository.YardRepository;
import com.maersk.sd1.sdy.controller.dto.GroupCodeRegisterOutputDTO;
import com.maersk.sd1.common.repository.GroupCodeRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class GroupCodeRegisterService {

    private static final Logger logger = LogManager.getLogger(GroupCodeRegisterService.class);

    private final GroupCodeRepository groupCodeRepository;
    private final YardRepository yardRepository;
    private final UserRepository userRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public GroupCodeRegisterOutputDTO registerGroupCode(Integer yardId,
                                                        String code,
                                                        String name,
                                                        Boolean active,
                                                        Integer userRegistrationId,
                                                        Integer languageId) {
        GroupCodeRegisterOutputDTO output = new GroupCodeRegisterOutputDTO();
        try {

                Optional<Yard> yardOptional = yardRepository.findById(yardId);
                if (yardOptional.isEmpty()) {
                    output.setRespEstado(0);
                    output.setRespMensaje("Invalid yard_id: " + yardId);
                    output.setRespNewId(0);
                    logger.info("Yard not found with ID: {}", yardId);
                    return output;
                }
                Yard yard = yardOptional.get();

                Optional<User> userOptional = userRepository.findById(userRegistrationId);
                if (userOptional.isEmpty()) {
                    output.setRespEstado(0);
                    output.setRespMensaje("Invalid user_registration_id: " + userRegistrationId);
                    output.setRespNewId(0);
                    logger.info("User not found with ID: {}", userRegistrationId);
                    return output;
                }
                User registrationUser = userOptional.get();

            GroupCode groupCode = new GroupCode();
            groupCode.setYard(yard);
            groupCode.setCode(code);
            groupCode.setName(name);
            groupCode.setActive(active);
            groupCode.setRegistrationDate(LocalDateTime.now());
            groupCode.setRegistrationUser(registrationUser);

            groupCode = groupCodeRepository.saveAndFlush(groupCode);
            Integer newId = groupCode.getId();
            logger.info("GroupCode registered with ID: {}", newId);

            String message = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, languageId);

            output.setRespEstado(1);
            output.setRespMensaje(message);
            output.setRespNewId(newId);
        } catch (Exception e) {
            logger.error("Error in registerGroupCode", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }
}