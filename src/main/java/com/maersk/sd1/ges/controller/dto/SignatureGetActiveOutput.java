package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class SignatureGetActiveOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("user_signature_uids")
    private List<String> userSignatureUids;

    @JsonProperty("person_signature_uids")
    private List<String> personSignatureUids;
}