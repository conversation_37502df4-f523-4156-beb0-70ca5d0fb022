package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SftpConfigObtenerOutput {

    @JsonProperty("resp_sftp_config_id")
    private Integer respSftpConfigId;

    @JsonProperty("resp_es_ftp")
    private Boolean respEsFtp;

    @JsonProperty("resp_alias")
    private String respAlias;

    @JsonProperty("resp_sftp_host")
    private String respSftpHost;

    @JsonProperty("resp_sftp_name")
    private String respSftpName;

    @JsonProperty("resp_sftp_pass")
    private String respSftpPass;

    @JsonProperty("resp_sftp_port")
    private String respSftpPort;

    @JsonProperty("resp_sftp_path")
    private String respSftpPath;

    @JsonProperty("resp_evento_despues_subir")
    private String respEventoDespuesSubir;

    @JsonProperty("resp_estado")
    private Boolean respEstado;

    @JsonProperty("resp_fecha_registro")
    private LocalDateTime respFechaRegistro;

    @JsonProperty("resp_fecha_modificacion")
    private LocalDateTime respFechaModificacion;

    @JsonProperty("resp_requiere_private_key")
    private Boolean respRequierePrivateKey;

    @JsonProperty("resp_adjuntos_public_key")
    private List<AttachmentDTO> respAdjuntosPublicKey;

    @JsonProperty("resp_adjuntos_private_key")
    private List<AttachmentDTO> respAdjuntosPrivateKey;

    @JsonProperty("resp_requiere_passphrase")
    private Boolean respRequierePassphrase;

    @JsonProperty("resp_passphrase")
    private String respPassphrase;

    @JsonProperty("resp_result_code")
    private Integer respResultCode;

    @JsonProperty("resp_result_message")
    private String respResultMessage;

    @Data
    public static class AttachmentDTO {
        @JsonProperty("adjunto_id")
        private Integer adjuntoId;

        @JsonProperty("id")
        private String id;

        @JsonProperty("nombre")
        private String nombre;

        @JsonProperty("formato")
        private String formato;

        @JsonProperty("peso")
        private Integer peso;

        @JsonProperty("ubicacion")
        private String ubicacion;

        @JsonProperty("tipo_adjunto")
        private Integer tipoAdjunto;

        @JsonProperty("descripcion")
        private String descripcion;

        @JsonProperty("url")
        private String url;

        @JsonProperty("tipo")
        private String tipo;
    }
}

