package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ChassisDocument;
import com.maersk.sd1.common.model.ChassisDocumentDetail;
import com.maersk.sd1.sdg.dto.ChassisBookingDetailsDto;
import com.maersk.sd1.sdg.dto.ChassisSearchResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;


public interface ChassisDocumentRepository extends JpaRepository<ChassisDocument, Integer> {

    @Query("""
            SELECT cdd FROM ChassisDocumentDetail cdd
            WHERE cdd.chassis.chassisNumber = :chassisNumber AND cdd.chassisDocument.subBusinessUnit.id = :subBusinessUnitId
            AND cdd.catStatusChassis.id = :chassisDocumentStatusId AND cdd.chassisDocument.catMovementType.id = :movementTypeId
            AND cdd.active = true AND cdd.chassisDocument.active = true
            """)
    Page<ChassisDocumentDetail> findActiveChassiDocumentByChassisNumberAndSubBusinessUnitId(Pageable pageable, String chassisNumber, Integer subBusinessUnitId, Integer chassisDocumentStatusId, Integer movementTypeId);

    @Query("""
                SELECT new com.maersk.sd1.sdg.dto.ChassisSearchResponse(
                    d.documentChassisNumber,
                    d.catReferenceType.id,
                    d.id,
                    b.catChassisType.id,
                    c.description,
                    t.description)
                FROM ChassisDocument d
                JOIN ChassisBookingDocument b ON d.id = b.chassisDocument.id
                JOIN ChassisDocumentDetail cd ON d.id = cd.chassisDocument.id
                JOIN Catalog c ON c.id = b.catChassisType.id
                JOIN Catalog t ON t.id = d.catChassisOperationType.id
                LEFT JOIN Company tra ON cd.transportCompany.id = tra.id
                WHERE d.catMovementType.id = :isGateOut
                  AND d.subBusinessUnit.id = :subBusinessUnitId
                  AND d.documentChassisNumber = :documentChassisNumber
                  AND cd.chassis.id IS NULL
                  AND d.active = true
                  AND b.active = true
                  AND cd.active = true
                  AND (SELECT COUNT(e) FROM EirChassis e
                       WHERE e.chassisDocumentGo.id = d.id AND e.active = true) <
                      (SELECT SUM(b.quanty) FROM ChassisBookingDocument b
                       WHERE b.chassisDocument.id = d.id AND b.active = true)
                ORDER BY d.registrationDate DESC
            """)
    List<ChassisSearchResponse> findChassisDataRaw(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("documentChassisNumber") String documentChassisNumber,
            @Param("isGateOut") Integer isGateOut,
            Pageable pageable
    );

    @Query("SELECT new com.maersk.sd1.sdg.dto.ChassisBookingDetailsDto(cdd.id, cbd.id) " +
            "FROM ChassisDocument cd " +
            "INNER JOIN ChassisBookingDocument cbd ON cd.id = cbd.chassisDocument.id " +
            "INNER JOIN ChassisDocumentDetail cdd ON cbd.id = cdd.chassisBookingDocument.id " +
            "WHERE cd.id = :documentChassisGoId " +
            "AND cbd.catChassisType.id = :catChassisTypeId " +
            "AND cdd.chassis.id IS NULL " +
            "AND cdd.catStatusChassis.id = :statusChassisPending " +
            "AND (COALESCE(cbd.quanty, 0) - COALESCE(cbd.attendedQuantity, 0)) > 0 " +
            "AND cd.active = true AND cbd.active = true AND cdd.active = true " +
            "ORDER BY cdd.id")
    Page<ChassisBookingDetailsDto> findDocumentChassisDetails(
            @Param("documentChassisGoId") Integer documentChassisGoId,
            @Param("catChassisTypeId") Integer catChassisTypeId,
            @Param("statusChassisPending") Integer statusChassisPending,
            Pageable pageable
    );

    @Query("SELECT d.documentChassisNumber, d.customerCompay.id " +
            "FROM ChassisDocument d " +
            "WHERE d.id = :docChassisId")
    List<Object[]> findDocChassisDetailsById(@Param("docChassisId") Integer docChassisId);

    @Query("""
            SELECT dc.id
                FROM ChassisDocument dc
                WHERE dc.documentChassisNumber = :chassisNumber AND dc.subBusinessUnit.id = :subBusinessUnitId AND dc.active = true
            """)
    Integer findActiveDocumentChassis(@Param("chassisNumber") String chassisNumber,
                                      @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Procedure(name = "DocumentChassis.registerGateOut")
    Map<String, Object> callDocumentChassisRegisterGateout(
            @Param("business_unit_id") Integer businessUnitId,
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("chassis_list") String chassisList,
            @Param("creation_source") String creationSource,
            @Param("comments") String comments,
            @Param("user_registration_id") Integer userRegistrationId,
            @Param("reference_type") Integer referenceType,
            @Param("reference") String reference,
            @Param("customer_id") Integer customerId,
            @Param("chassis_type_operation") Integer chassisTypeOperation,
            @Param("language_id") Integer languageId
    );

    @Query("SELECT dc.id " +
            "FROM ChassisDocument dc " +
            "WHERE dc.registrationUser.id = :userRegistrationId " +
            "AND dc.subBusinessUnit.id = :subBusinessUnitId " +
            "AND dc.catMovementType.id = :catMovementTypeId ")
    List<Integer> findLatestDocumentChassisId(@Param("userRegistrationId") Integer userRegistrationId,
                                              @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                              @Param("catMovementTypeId") Integer catMovementTypeId,
                                              Pageable pageable);

    @Procedure(name = "ChassisDocument.documentChassisGateOutGenerated")
    HashMap<String, Object> documentChassisGateOutGenerated(@Param("sub_business_unit_local_id") Integer subBusinessUnitLocalId, @Param("chassis") String chassis, @Param("user_registration_id") Integer userRegistrationId, @Param("language_id") Integer paramId);

    @Query("SELECT cd FROM ChassisDocument cd WHERE cd.id = :chassisDocumentId AND cd.subBusinessUnit.id = :subBusinessUnitId AND active = true")
    Optional<ChassisDocument> findActiveChassisDocumentByIdAndSubBusinessUnit(Integer chassisDocumentId, Integer subBusinessUnitId);

    @Query("""
            SELECT cd
            FROM ChassisDocument cd
            WHERE cd.documentChassisNumber = :docNumber
              AND cd.subBusinessUnit.id = :subBusinessUnitId
              AND cd.catMovementType.id = :catMovementTypeId
              AND (:referenceType IS NULL OR cd.catReferenceType.id = :referenceType)
              AND cd.active = TRUE
            """)
    Optional<ChassisDocument> findActiveDocumentChassis(@Param("docNumber") String docNumber,
                                                        @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                        @Param("catMovementTypeId") Integer catMovementTypeId,
                                                        @Param("referenceType") Long referenceType);

    @Query(value = "select cd from ChassisDocument cd where cd.id in :ids and cd.active = true")
    List<ChassisDocument> findByIds(@Param("ids") List<Integer> ids);

}