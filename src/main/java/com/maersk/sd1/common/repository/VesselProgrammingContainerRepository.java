package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.VesselProgrammingContainer;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.sde.dto.FechaSobrestadiaItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface VesselProgrammingContainerRepository extends JpaRepository<VesselProgrammingContainer, Integer> {
    @Query("select v from VesselProgrammingContainer v where v.container.id = :containerId and v.vesselProgrammingDetail.id = :vesselProgrammingDetailId")
    VesselProgrammingContainer findByContainer_IdAndVesselProgrammingDetail_Id(@Param("containerId") Integer containerId, @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Query("""
            select v from VesselProgrammingContainer v
            where v.container.id = :containerId and v.vesselProgrammingDetail.id = :vesselProgrammingDetailId and v.active = true""")
    VesselProgrammingContainer findByContainer_IdAndVesselProgrammingDetail_IdAndActiveTrue(@Param("containerId") Integer containerId, @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Modifying
    @Query("""
                UPDATE VesselProgrammingContainer vpc
                SET vpc.receivedQuantity = 0,
                    vpc.receivedWeight = 0,
                    vpc.catReceivedWeightMeasure.id = null,
                    vpc.receivedSeal1 = null,
                    vpc.receivedSeal2 = null,
                    vpc.receivedSeal3 = null,
                    vpc.receivedSeal4 = null,
                    vpc.modificationDate = :modificationDate,
                    vpc.traceProgVesCnt = 'del-assignment-gif',
                    vpc.modificationUser.id = :userModificationId
                WHERE vpc.container.id = :containerId
                  AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId
                  And vpc.active = true
            """)
    void resetReception(Integer containerId, Integer vesselProgrammingDetailId, Integer userModificationId, LocalDateTime modificationDate);

    @Modifying
    @Query("""
                UPDATE VesselProgrammingContainer vpc
                SET vpc.dispatchedSeal1 = null,
                    vpc.dispatchedSeal2 = null,
                    vpc.dispatchedSeal3 = null,
                    vpc.dispatchedSeal4 = null,
                    vpc.modificationUser.id = :userModificationId,
                    vpc.modificationDate = :modificationDate,
                    vpc.traceProgVesCnt = 'del-assignment-gif'
                WHERE vpc.container.id = :containerId
                  AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId
                  And vpc.active = true
            """)
    void resetDispatch(Integer containerId, Integer vesselProgrammingDetailId, Integer userModificationId, LocalDateTime modificationDate);

    @Query(value = "select vpc from VesselProgrammingContainer vpc " +
            "where vpc.id = :id " +
            "and vpc.container.id = :containerId " +
            "and vpc.active = true")
    VesselProgrammingContainer findByIdAndContainerIdAndActiveTrue(@Param("id") Integer id, @Param("containerId") Integer containerId);

    @Query(value = "select v from Eir e " +
            "inner join VesselProgrammingContainer v on v.vesselProgrammingDetail.id = e.vesselProgrammingDetail.id " +
            "and e.container.id = v.container.id and v.active = true")
    List<VesselProgrammingContainer> findByEirIdList(List<Integer> eirIdList);
    @Query(value = """
            SELECT  t2.contenedor_id,
                    t2.programacion_nave_detalle_id,
                    LTRIM(STUFF((SELECT ', ' + IIF(RTRIM(mimox.codigo_imo) = '9', '9 (' + pncimox.imo_others + ')', RTRIM(mimox.codigo_imo))
                                 FROM sds.programacion_nave_contenedor_imo AS pncimox (NOLOCK)
                                 INNER JOIN sds.imo AS mimox (NOLOCK) ON pncimox.imo_id = mimox.imo_id
                                 WHERE pncimox.programacion_nave_contenedor_id = t2.programacion_nave_contenedor_id
                                 AND pncimox.activo = 1
                                 ORDER BY mimox.codigo_imo ASC FOR XML PATH('')), 1, 1, '')) AS imo
            FROM [sds].[programacion_nave_contenedor] AS t2
            WHERE t2.activo = 1
            AND t2.contenedor_id IN :containerIds
            AND t2.programacion_nave_detalle_id IN :vesselProgrammingDetailIds
            """, nativeQuery = true)
    List<Object[]> findImoByContainerIdsAndVesselProgrammingDetailIds(
            @Param("containerIds") Set<Integer> containerIds,
            @Param("vesselProgrammingDetailIds") Set<Integer> vesselProgrammingDetailIds);

    @Query("SELECT v FROM VesselProgrammingContainer v " +
            "WHERE v.container.id IN :containerIds " +
            "AND v.vesselProgrammingDetail.id IN :programmingShipDetailIds " +
            "AND v.active = true")
    List<VesselProgrammingContainer> findActiveVesselContainers(
            @Param("containerIds") List<Integer> containerIds,
            @Param("programmingShipDetailIds") List<Integer> programmingShipDetailIds);


    Optional<VesselProgrammingContainer> findByContainerIdAndVesselProgrammingDetailId(Integer containerId,
                                                                                       Integer vesselProgrammingDetailId);

    @Query(value="select v from VesselProgrammingContainer v " +
            "where v.container.id = :containerId and v.vesselProgrammingDetail.id = :vesselProgrammingDetailId and v.active = true")
    Optional<VesselProgrammingContainer> findTopByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(Integer containerId,
                                                                                                       Integer vesselProgrammingDetailId);



           @Query("""
            SELECT cdd FROM VesselProgrammingContainer vpc
            INNER JOIN CargoDocumentDetail cdd ON (cdd.container.id = vpc.container.id)
            INNER JOIN cdd.cargoDocument cdc
            INNER JOIN VesselProgrammingDetail vpd ON (vpd.id = cdc.vesselProgrammingDetail.id AND vpd.id = vpc.vesselProgrammingDetail.id)
            WHERE vpc.container.id = :containerId 
            AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId 
            AND vpd.active = true 
            AND cdc.active = true 
            AND cdd.active = true 
            AND vpc.active = true 
            """)
    Optional<CargoDocumentDetail> findActiveVesselProgrammingContainerByContainerIdAndProgrammingDetailId(Integer containerId, Integer vesselProgrammingDetailId);
    Optional<VesselProgrammingContainer> findByVesselProgrammingDetailIdAndContainerIdAndActive(
            Integer vesselProgrammingDetailId,
            Integer containerId,
            Boolean active);

    @Query("""
            SELECT p FROM VesselProgrammingContainer p
            WHERE p.container = :container
              AND p.vesselProgrammingDetail = :vesselProgrammingDetail
            """)
    Optional<VesselProgrammingContainer> findByContainerAndVesselProgrammingDetail(
            @Param("container") Container container,
            @Param("vesselProgrammingDetail") VesselProgrammingDetail vesselProgrammingDetail
    );


    @Query("""
            SELECT new com.maersk.sd1.sde.dto.FechaSobrestadiaItem(
                doc.cargoDocument AS cargoDocument,
                sl.shippingLineCompany AS lineaNaviera,
                c.id AS contenedorId,
                c.containerNumber AS contenedor,
                vpc.demurrageDate AS fechaSobrestadia
            )
            FROM CargoDocumentDetail docDetail
            JOIN docDetail.cargoDocument doc
            JOIN doc.vesselProgrammingDetail vpd
            JOIN vpd.catOperation catOp
            JOIN vpd.vesselProgramming vp
            JOIN docDetail.container c
            JOIN doc.shippingLine sl
            JOIN VesselProgrammingContainer vpc ON vpc.vesselProgrammingDetail = vpd AND vpc.container = c
            WHERE doc.id = :documentoCargaId
              AND catOp.id = 43001
              AND c IS NOT NULL
              AND doc.active = true
              AND docDetail.active = true
              AND vpd.active = true
              AND vp.active = true
              AND vpc.active = true
            ORDER BY c.containerNumber ASC
            """)
    List<FechaSobrestadiaItem> findFechaSobrestadiaByDocumentoCargaId(@Param("documentoCargaId") Integer documentoCargaId);


    @Query("SELECT COUNT(1) FROM VesselProgrammingContainer vpc "
            + "WHERE vpc.active = true "
            + "AND vpc.vesselProgrammingDetail.vesselProgramming.id = :programacionNaveId")
    int countActiveVesselProgrammingContainersByProgramacionNaveId(@Param("programacionNaveId") Integer programacionNaveId);


    int countByVesselProgrammingDetailAndActiveTrue(VesselProgrammingDetail detail);


    @Modifying
    @Query("UPDATE VesselProgrammingContainer v SET v.isDangerousCargo = true WHERE v.id = :id")
    int updateDangerousCargo(Integer id);

    @Modifying
    @Query("UPDATE VesselProgrammingContainer vpc SET vpc.manifestedSeal1 = :seal1, vpc.manifestedSeal2 = :seal2, "
            + "vpc.manifestedSeal3 = :seal3, vpc.manifestedSeal4 = :seal4, "
            + "vpc.modificationUser.id = :modificationUserId, vpc.modificationDate = CURRENT_TIMESTAMP, "
            + "vpc.traceProgVesCnt = 'upd_planning_gifU1' "
            + "WHERE vpc.vesselProgrammingDetail.id = :vpcId "
            + "AND vpc.container.id = :containerId")
    int updateVesselProgrammingContainerSeals(@Param("vpcId") Integer vpcId,
                                              @Param("seal1") String seal1,
                                              @Param("seal2") String seal2,
                                              @Param("seal3") String seal3,
                                              @Param("seal4") String seal4,
                                              @Param("modificationUserId") Integer modificationUserId,
                                              @Param("containerId") Integer containerId);

    @Query("""
                SELECT v.id
                FROM VesselProgrammingContainer v
                WHERE v.container.id = :containerId
                  AND v.vesselProgrammingDetail.id = :vesselProgrammingDetailId
            """)
    Optional<Integer> findId(@Param("containerId") Integer containerId,
                             @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId);

    @Query("""
            SELECT vpc.id FROM VesselProgrammingContainer vpc
            JOIN vpc.vesselProgrammingDetail vpd
            JOIN CargoDocument cd ON vpd.id = cd.vesselProgrammingDetail.id
            JOIN CargoDocumentDetail cdd ON cd.id = cdd.cargoDocument.id
            JOIN TransportPlanningDetail tpd ON cdd.id = tpd.cargoDocumentDetail.id
            JOIN TransportPlanning tp ON tpd.transportPlanning.id = tp.id
            WHERE tp.id = :transportPlanningId
            AND vpc.container.id = cdd.container.id
            AND vpc.container.id = :containerId
            """)
    List<Integer> findVesselProgrammingContainerIds(
            @Param("transportPlanningId") Integer transportPlanningId,
            @Param("containerId") Integer containerId
    );

    @Modifying
    @Query("""
            UPDATE VesselProgrammingContainer vpc
            SET vpc.active = false,
                vpc.modificationUser.id = :userRegistrationId,
                vpc.modificationDate = CURRENT_TIMESTAMP
            WHERE vpc.id IN :containerIds
            """)
    int deactivateVesselProgrammingContainers(
            @Param("userRegistrationId") Integer userRegistrationId,
            @Param("containerIds") List<Integer> containerIds
    );

    @Query("""
                SELECT vpc.id
                FROM VesselProgrammingContainer vpc
                JOIN vpc.vesselProgrammingDetail vpd
                JOIN CargoDocument cd ON vpd.id = cd.vesselProgrammingDetail.id
                JOIN CargoDocumentDetail cdd ON cdd.cargoDocument.id = cd.id
                JOIN TransportPlanningDetail tpd ON tpd.cargoDocumentDetail.id = cdd.id
                JOIN tpd.transportPlanning tp
                JOIN Container con ON con.id = cdd.container.id
                WHERE tp.id = :transportPlanningId
                AND con.containerNumber NOT IN :tableContainers
                AND tpd.active = true
                AND cdd.active = true
                AND vpc.active = true
                AND cdd.container.id = vpc.container.id
            """)
    List<Integer> findVesselProgrammingContainerIds(
            @Param("transportPlanningId") Integer transportPlanningId,
            @Param("tableContainers") List<String> tableContainers
    );

    @Query("""
            SELECT vpc
            FROM VesselProgrammingContainer vpc
            WHERE vpc.container.id = :containerId
              AND vpc.vesselProgrammingDetail.id = :vesselProgrammingDetailId
            """)
    List<VesselProgrammingContainer> findByContainerAndVesselProgrammingDetail(
            @Param("containerId") Integer containerId,
            @Param("vesselProgrammingDetailId") Integer vesselProgrammingDetailId
    );

    @Query(value = """
            select
            pd.programacion_nave_id,
            pc.programacion_nave_detalle_id, pc.programacion_nave_contenedor_id,
            pn.unidad_negocio_id, pn.sub_unidad_negocio_id, 
            dc.documento_carga_id, dc.documento_carga, convert(varchar, dc.fecha_registro, 103) as fecha_registro, dc.activo as documento_carga_activo, 
            dc.puerto_descarga_id, p.nombre as puerto_nombre, p.puerto, p.activo as puerto_activo, p.pais_id, 
            n.nave_id, n.nombre as nave_nombre, n.nave, n.imo_number, n.activo as nave_activo, 
            pn.viaje, 
            pc.contenedor_id, e.numero_contenedor, pc.es_carga_peligrosa, p.activo as contenedor_activo, 
            dc.linea_naviera_id, l.nombre as linea_naviera_nombre, l.linea_naviera, l.activo as linea_naviera_activo, 
            dc.empresa_consignatario_id, b.razon_social, b.direccion, 
            c.catalogo_id as cat_operacion_id, c.catalogo_padre_id as cat_operacion_padre_id, c.codigo as cat_operacion_codigo, c.descripcion as cat_operacion_descripcion, c.estado as cat_operacion_estado, 
            c1.catalogo_id as cat_origen_carga_id, c1.catalogo_padre_id as cat_origen_carga_padre_id, c1.codigo as cat_origen_carga_codigo, c1.descripcion as cat_origen_carga_descripcion, c1.estado as cat_origen_carga_estado, 
            c2.catalogo_id as cat_condicion_carga_id, c2.catalogo_padre_id as cat_condicion_carga_padre_id, c2.codigo as cat_condicion_carga_codigo, c2.descripcion as cat_condicion_carga_descripcion, c2.estado as cat_condicion_carga_estado, 
            c3.catalogo_id as cat_movimiento_id, c3.catalogo_padre_id as cat_movimiento_padre_id, c3.codigo as cat_movimiento_codigo, c3.descripcion as cat_movimiento_descripcion, c3.estado as cat_movimiento_estado, 
            c4.catalogo_id as cat_procedencia_id, c4.catalogo_padre_id as cat_procedencia_padre_id, c4.codigo as cat_procedencia_codigo, c4.descripcion as cat_procedencia_descripcion, c4.estado as cat_procedencia_estado, 
            c5.catalogo_id as cat_empty_full_id, c5.catalogo_padre_id as cat_empty_full_padre_id, c5.codigo as cat_empty_full_codigo, c5.descripcion as cat_empty_full_descripcion, c5.estado as cat_empty_full_estado, 
            eir.eir_id, 
            c6.catalogo_id as cat_structure_condition_id, c6.catalogo_padre_id as cat_structure_condition_padre_id, c6.codigo as cat_structure_condition_codigo, c6.descripcion as cat_structure_condition_descripcion, c6.estado as cat_structure_condition_estado, 
            
            c7.catalogo_id as cat_machine_condition_id, c7.catalogo_padre_id as cat_machine_condition_padre_id, c7.codigo as cat_machine_condition_codigo, c7.descripcion as cat_machine_condition_descripcion, c7.estado as cat_machine_condition_estado 
            
            
            from sds.programacion_nave_contenedor pc 
            left outer join sds.contenedor e on e.contenedor_id = pc.contenedor_id 
            inner join sds.programacion_nave_detalle pd on pd.programacion_nave_detalle_id = pc.programacion_nave_detalle_id 
            inner join sds.documento_carga dc on dc.programacion_nave_detalle_id = pd.programacion_nave_detalle_id 
            inner join sds.documento_carga_detalle dd on dd.documento_carga_id = dc.documento_carga_id and dd.contenedor_id = e.contenedor_id 
            inner join sds.programacion_nave pn on pn.programacion_nave_id = pd.programacion_nave_id 
            left outer join sde.eir eir on eir.programacion_nave_detalle_id = pd.programacion_nave_detalle_id and eir.contenedor_id = e.contenedor_id 
            and eir.cat_movimiento_id = CASE WHEN :movementType = 'I' THEN :eirMovementTypeEntry 
            WHEN :movementType ='S' THEN :eirMovementTypeExit 
            ELSE :eirMovementTypeEntry 
            END 
            left outer join sde.eir_actividad_zona ea on ea.eir_id = eir.eir_id 
            left outer join sds.nave n on n.nave_id = pn.nave_id 
            left outer join ges.empresa b on b.empresa_id = dc.empresa_consignatario_id 
            left outer join sds.puerto p on p.puerto_id = dc.puerto_descarga_id 
            left outer join sds.linea_naviera l on l.linea_naviera_id = dc.linea_naviera_id 
            
            left outer join ges.catalogo c on c.catalogo_id = pd.cat_operacion_id 
            left outer join ges.catalogo c1 on c1.catalogo_id = dc.cat_origen_carga_id 
            left outer join ges.catalogo c2 on c2.catalogo_id = dd.cat_condicion_carga_id 
            left outer join ges.catalogo c3 on c3.catalogo_id = eir.cat_movimiento_id 
            left outer join ges.catalogo c4 on c4.catalogo_id = eir.cat_procedencia_id 
            left outer join ges.catalogo c5 on c5.catalogo_id = eir.cat_empty_full_id 
            left outer join ges.catalogo c6 on c6.catalogo_id = sdg.fn_GetEquipmentConditionID(eir.eir_id,:isContainer,'S','CUR') 
            left outer join ges.catalogo c7 on c7.catalogo_id = sdg.fn_GetEquipmentConditionID(eir.eir_id,:isContainer,'M','CUR') 
            where pn.activo = 1 and pd.activo = 1 and pc.activo = 1 
            and isnull(ea.eir_actividad_zona_id, 0) = isnull((select max(eir_actividad_zona_id) from sde.eir_actividad_zona az where az.eir_id = eir.eir_id), 0) 
            and pn.sub_unidad_negocio_id = :businessUnitId 
            order by e.numero_contenedor """, nativeQuery = true)
    List<Object[]> getShipPlanAndScheduleDetails(@Param("movementType") String movementType,
                                                 @Param("eirMovementTypeEntry") Integer eirMovementTypeEntry,
                                                 @Param("eirMovementTypeExit") Integer eirMovementTypeExit,
                                                 @Param("isContainer") Integer isContainer,
                                                 @Param("businessUnitId") Integer businessUnitId);

}