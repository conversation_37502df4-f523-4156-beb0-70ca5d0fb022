package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.NotificationRole;
import com.maersk.sd1.common.model.NotificationRoleId;
import com.maersk.sd1.common.model.Role;
import com.maersk.sd1.seg.dto.NotificationDetailDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface NotificationRoleRepository extends JpaRepository<NotificationRole, NotificationRoleId> {

    @Transactional
    @Modifying
    @Query("INSERT INTO NotificationRole (notification.id, role.id) " +
            "SELECT :notificationId, r.id FROM Role r WHERE r.id IN :roleIds")
    void insertNotificationRoles(@Param("notificationId") int notificationId, @Param("roleIds") List<Integer> roleIds);

    @Query("SELECT DISTINCT nr.id.notificationId FROM NotificationRole nr WHERE nr.role.id IN :roles")
    List<Integer> findNotificationIdsByRoles(@Param("roles") List<Integer> roles);

    @Query("SELECT new com.maersk.sd1.seg.dto.NotificationDetailDTO(r.id, r.name) " +
            "FROM NotificationRole nr JOIN nr.role r WHERE nr.notification.id = :notificationId ORDER BY r.name ASC")
    List<NotificationDetailDTO> findRolesByNotificationId(@Param("notificationId") Integer notificationId);

    @Transactional
    @Modifying
    void deleteByRole(Role role);

    @Query("SELECT n.title FROM NotificationRole nr JOIN nr.notification n WHERE nr.role.id = :roleId")
    List<String> findNotificationTitlesByRoleId(@Param("roleId") Integer roleId);
}