package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Report;
import com.maersk.sd1.ges.dto.ReportJsonDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.maersk.sd1.ges.dto.ReportDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface ReportRepository extends JpaRepository<Report, Integer> {
  
    @Query("SELECT r FROM Report r WHERE r.id = :reportId")
    Optional<Report> findByReportId(@Param("reportId") Integer reportId);

    @Query(value = "SELECT rol_id FROM ges.reporte_rol WHERE reporte_id = :reporte_id", nativeQuery = true)
    List<Integer> findRolIdsByReportId(@Param("reporte_id") Integer reporteId);

    @Query(
            "SELECT r FROM Report r " +
                    "JOIN r.menu m " +
                    "WHERE (:status IS NULL OR r.status = :status) " +
                    "AND (:name IS NULL OR r.name LIKE CONCAT('%', :name, '%')) " +
                    "AND (:nameStore IS NULL OR r.nameStore LIKE CONCAT('%', :nameStore, '%')) " +
                    "ORDER BY r.id DESC"
    )
    Page<Report> searchReports(
            @Param("status") Boolean status,
            @Param("name") String name,
            @Param("nameStore") String nameStore,
            Pageable pageable
    );

        @Query(value = """
             SELECT REP.reporte_id AS reportId,
                 ( SELECT ROL.id, ROL.nombre
                   FROM ges.reporte_rol RER
                   INNER JOIN seg.rol ROL ON ROL.rol_id = RER.rol_id
                   WHERE
                        RER.reporte_id = REP.reporte_id AND
                        ROL.id IS NOT NULL
                        FOR JSON PATH ) AS roles
             FROM ges.reporte REP
             INNER JOIN seg.menu MEN ON MEN.menu_id = REP.menu_id
             WHERE
                (:estado IS NULL OR REP.estado = :estado) AND
                (:nombre IS NULL OR REP.nombre LIKE :nombre) AND
                (:nombre_store IS NULL OR REP.nombre_store LIKE :nombre_store)
             ORDER BY 1 DESC
        """
        , nativeQuery = true)
        List<ReportJsonDTO> findData(@Param("estado") String status,
                             @Param("nombre") String names,
                             @Param("nombre_store") String namesStore);

    long countById1AndIdNot(String id1, Integer id);

    @Modifying
    @Transactional
    @Query(value = "INSERT INTO ges.reporte_rol (reporte_id, rol_id) " +
            "SELECT :respNewId, rol_id " +
            "FROM OPENJSON(:rolesId) " +
            "WITH (rol_id INT '$.rol_id')",
            nativeQuery = true)
    void insertReportRoles(@Param("respNewId") Integer respNewId,
                           @Param("rolesId") String rolesId);


    @Transactional
    @Modifying
    @Query("DELETE FROM Report r WHERE r.id = :reportId")
    void deleteByReportId(@Param("reportId") Integer reportId);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM ges.reporte_rol WHERE reporte_id = :reportId", nativeQuery = true)
    void deleteByReportId1(@Param("reportId") Integer reportId);


    @Query("SELECT new com.maersk.sd1.ges.dto.ReportDTO(r.id, CONCAT(m.title, ' - ', r.name), r.description, r.nameStore, r.parameters) " +
            "FROM Report r INNER JOIN r.menu m WHERE m.status = true AND r.id IN " +
            "(SELECT rr.report FROM ReportRole rr INNER JOIN UserRole ur ON ur.role.id = rr.role.id WHERE ur.user.id = :userId)")
    List<ReportDTO> findReportesByUserId(@Param("userId") int userId);
}