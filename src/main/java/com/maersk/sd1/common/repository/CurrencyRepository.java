package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Currency;
import com.maersk.sd1.seg.dto.CurrencyOptionDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.Modifying;

import java.time.LocalDateTime;
import java.util.List;

public interface CurrencyRepository extends JpaRepository<Currency, Integer> {

    @Query(value = "exec ges.listar_monedas :activeCatalogStatus",nativeQuery = true)
    List<Object[]> getCurrencyList(@Param("activeCatalogStatus") Integer activeCatalogStatus);
  
    @Modifying
    @Query("DELETE FROM Company comp WHERE comp.businessUnit.id = :unidadNegocioId")
    void deleteCompanyByBusinessUnit(@Param("unidadNegocioId") Integer unidadNegocioId);

     @Query("SELECT new com.maersk.sd1.seg.dto.CurrencyOptionDto(c.id, c.name, c.abbreviation) "
            + "FROM Currency c "
            + "WHERE c.status = :status")
    List<CurrencyOptionDto> findCurrencyOptionsByStatus(@Param("status") Integer status);
  
    @Modifying
    @Query("UPDATE Currency c " +
            "SET c.name = :nombre, " +
            "    c.abbreviation = :abreviatura, " +
            "    c.symbol = :simbolo, " +
            "    c.status = :estado, " +
            "    c.modificationUser.id = :usuarioModificacionId, " +
            "    c.separatorMiles = :separadorMiles, " +
            "    c.separatorDecimals = :separadorDecimales, " +
            "    c.precision = :precisionVal, " +
            "    c.icu = :icu, " +
            "    c.modificationDate = :modificationDate " +
            "WHERE c.id = :monedaId")
    void updateCurrency(@Param("monedaId") Integer monedaId,
                        @Param("nombre") String nombre,
                        @Param("abreviatura") String abreviatura,
                        @Param("simbolo") String simbolo,
                        @Param("estado") Integer estado,
                        @Param("usuarioModificacionId") Integer usuarioModificacionId,
                        @Param("separadorMiles") String separadorMiles,
                        @Param("separadorDecimales") String separadorDecimales,
                        @Param("precisionVal") String precisionVal,
                        @Param("icu") String icu,
                        @Param("modificationDate") LocalDateTime modificationDate);
}