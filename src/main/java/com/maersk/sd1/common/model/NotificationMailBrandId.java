package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class NotificationMailBrandId implements Serializable {
    private static final long serialVersionUID = -235607878446560468L;
    @NotNull
    @Column(name = "tipo_referencia_id", nullable = false)
    private Integer typeReferenceId;

    @NotNull
    @Column(name = "id", nullable = false)
    private Integer id;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        NotificationMailBrandId entity = (NotificationMailBrandId) o;
        return Objects.equals(this.typeReferenceId, entity.typeReferenceId) &&
                Objects.equals(this.id, entity.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(typeReferenceId, id);
    }

}