package com.maersk.sd1.common.model;

import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_job_email_marca", schema = "seg")
public class NotificationJobEmailBrand {
    @EmbeddedId
    private NotificationJobEmailBrandId id;

    @MapsId("notificationJobId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_job_id", nullable = false)
    private NotificationJob notificacionJob;

}