package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ChassisDocument;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "document_chassis_booking", schema = "sdh", indexes = {
        @Index(name = "DOCCHABK_DOCCHA_IDX", columnList = "document_chassis_id")
})
public class ChassisBookingDocument {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "booking_chassis_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_chassis_id")
    private ChassisDocument chassisDocument;

    @NotNull
    @Column(name = "quanty", nullable = false)
    private Integer quanty;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_type_id", nullable = false)
    private Catalog catChassisType;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @Column(name = "registration_date")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Column(name = "quantity_attended")
    private Integer attendedQuantity;

}