package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.UserSystemConfigId;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "usuario_sistema_config", schema = "seg")
public class UserSystemConfig {
    @EmbeddedId
    private UserSystemConfigId id;

    @MapsId("userId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User user;

    @MapsId("systemId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sistema_id", nullable = false)
    private System system;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "idioma_id", nullable = false)
    private Language language;

}