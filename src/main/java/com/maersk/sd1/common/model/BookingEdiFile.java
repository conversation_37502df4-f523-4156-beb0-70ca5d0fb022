package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BookingEdi;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "edi_coparn_archivo", schema = "sds", indexes = {
        @Index(name = "EDICOPARCH_EDICOP", columnList = "edi_coparn_id")
})
public class BookingEdiFile {
    @Id
    @Column(name = "edi_coparn_archivo_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "edi_coparn_id", nullable = false)
    private BookingEdi bookingEdi;

    @Lob
    @Column(name = "edi_coparn_contenido")
    private String bkEdiContent;

    @Lob
    @Column(name = "edi_coparn_json")
    private String bkEdiJson;

    @Column(name = "fecha_registro")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_registro_id")
    private User registrationUser;

    @Column(name = "fecha_modificacion_coparn")
    private LocalDateTime dateModificationCoparn;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "activo")
    private Boolean active;

    @Size(max = 1000)
    @Column(name = "azure_url", length = 1000)
    private String azureUrl;

    @Lob
    @Column(name = "edi_coparn_contenido_ori")
    private String bkEdiOriginContent;

}