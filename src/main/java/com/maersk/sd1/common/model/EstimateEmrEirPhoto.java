package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Attachment;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.EstimateEmr;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "estimado_emr_eir_foto", schema = "sde", indexes = {
        @Index(name = "ESTEMR_FOTO_EIR", columnList = "eir_id"),
        @Index(name = "ESTEMR_FOTO_ESTEMR", columnList = "estimado_emr_id")
})
public class EstimateEmrEirPhoto {

    @Id
    @Column(name = "estimado_emr_eir_foto_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "estimado_emr_id")
    private EstimateEmr estimadoEmr;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "adjunto_id", nullable = false)
    private Attachment attachment;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 20)
    @Column(name = "trace_est_eir_photo", length = 20)
    private String traceEstEirPhoto;

}