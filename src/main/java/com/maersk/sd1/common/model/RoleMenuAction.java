package com.maersk.sd1.common.model;

import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "rol_menu_accion", schema = "seg")
public class RoleMenuAction {
    @EmbeddedId
    private RoleMenuActionId id;

    @MapsId("id")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumns({
            @JoinColumn(name = "rol_id", referencedColumnName = "rol_id", nullable = false),
            @JoinColumn(name = "menu_id", referencedColumnName = "menu_id", nullable = false)
    })
    private RoleMenu rolMenu;

    @MapsId("typeActionId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_accion_id", nullable = false)
    private Catalog catActionType;

}