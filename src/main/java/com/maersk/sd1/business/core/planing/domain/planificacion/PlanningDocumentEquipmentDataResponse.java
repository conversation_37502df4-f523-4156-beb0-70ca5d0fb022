package com.maersk.sd1.business.core.planing.domain.planificacion;

import java.util.List;

import com.maersk.sd1.business.core.planing.domain.Bloque;
import com.maersk.sd1.business.core.planing.domain.Celda;
import com.maersk.sd1.business.core.planing.domain.Nivel;
import com.maersk.sd1.business.core.planing.domain.Patio;
import com.maersk.sd1.business.core.planing.domain.Tipo;
import com.maersk.sd1.business.core.planing.domain.UbicacionContenedor;
import com.maersk.sd1.business.core.planing.domain.UnidadNegocio;
import com.maersk.sd1.business.core.planing.domain.Usuario;
import com.maersk.sd1.business.infraestructure.shared.datamodel.contenedor_entity;
import com.maersk.sd1.business.infraestructure.shared.datamodel.ubicacion_entity;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PlanningDocumentEquipmentDataResponse {
	private Usuario usuario;
	private UnidadNegocio unidad_negocio;
	private Patio patio;
	private List<contenedor_entity> equipments;
	private Tipo tipo_movimiento;
	private Tipo tipo_movimiento_interno;
	private ubicacion_entity equipment_location_proposed;
	
	public UbicacionContenedor GetEquipmentLocationProposed() {
		var base = new UbicacionContenedor(
				new Bloque(equipment_location_proposed.getBloque_id(), equipment_location_proposed.getPatio_id(), equipment_location_proposed.getCat_bloque_id(),
						equipment_location_proposed.getUnidad_negocio_id(), equipment_location_proposed.getCodigo(), equipment_location_proposed.getNombre(),
						equipment_location_proposed.getFilas(), equipment_location_proposed.getColumnas(), equipment_location_proposed.getNiveles(), "", equipment_location_proposed.isActivo(),
						equipment_location_proposed.getBloque_cantidad_contenedores(), equipment_location_proposed.getBloque_tipo_codigo()),
				new Celda(equipment_location_proposed.getCelda_id(), equipment_location_proposed.getBloque_id(), equipment_location_proposed.getFila(),
						equipment_location_proposed.getColumna(), equipment_location_proposed.getIndice_fila(), equipment_location_proposed.getIndice_columna(),
						equipment_location_proposed.isBloqueado()),
				new Nivel(equipment_location_proposed.getNivel_id(), equipment_location_proposed.getCelda_id(), equipment_location_proposed.getIndice()));
		base.setActivo(true);
		base.setBloque_id(equipment_location_proposed.getBloque_id());
		base.setCelda_id(equipment_location_proposed.getCelda_id());
		base.setCelda_name(equipment_location_proposed.getFila() + "-" + equipment_location_proposed.getColumna());
		base.setColumna(equipment_location_proposed.getColumna());
		base.setFila(equipment_location_proposed.getFila());
		base.setIndice_columna(equipment_location_proposed.getIndice_columna());
		base.setIndice_fila(equipment_location_proposed.getIndice_fila());
		base.setNivel_id(equipment_location_proposed.getNivel_id());
		base.setPatio_id(equipment_location_proposed.getPatio_id());
		base.setUbicacion_contenedor_id(equipment_location_proposed.getUbicacion_contenedor_id());
		base.setContenedor_id(equipment_location_proposed.getContenedor_id());

		return base;
	}
}
