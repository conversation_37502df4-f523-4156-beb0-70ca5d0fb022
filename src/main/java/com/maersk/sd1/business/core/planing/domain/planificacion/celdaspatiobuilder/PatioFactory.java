package com.maersk.sd1.business.core.planing.domain.planificacion.celdaspatiobuilder;

import java.util.Collection;

import com.maersk.sd1.business.core.planing.domain.Bloque;
import com.maersk.sd1.business.core.planing.domain.Celda;

public interface PatioFactory {
	
    public Collection<Celda> AgruparCeldas(Bloque bloque, 
    		Collection<Celda> celdas, 
    		Collection<Celda> celdas_ocupadas, 
    		boolean isRowOrderDescendent) throws Exception;

}
