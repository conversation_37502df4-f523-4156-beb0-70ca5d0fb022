package com.maersk.sd1.business.core.planing.domain;

import java.util.Collection;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RangoReglaPlanificacionPatio {
	private int patio_id;
	private int rango_regla_planificacion_patio_id;
	private int regla_planificacion_patio_id;
	private int bloque_id;
	
	private int celda_id;
	
	private Integer indice_columna_desde;
	private Integer indice_columna_hasta;
	private Integer indice_fila_desde;
	private Integer indice_fila_hasta;

	private boolean activo;
	private int usuario_registro_id;
	private Date fecha_registro;
	private Integer usuario_modificacion_id;
	private Date fecha_modificacion;

	private Integer cat_bloque_id;
	private String cat_bloque_codigo;
	private String bloque_codigo;
	private String bloque_nombre;
	private boolean bloque_activo;
    public int cantidad_contenedores;
    public int filas;
    public int columnas;
    public int niveles;
    public String etiqueta_fila_40;
    public String etiqueta_fila_20;
    public String etiquetas_columna;
    public String configuracion;
	public Bloque Bloque;
	
    public boolean IsRowOrderDescendent;
    public boolean IsColOrderDescendent;    
	private Collection<Celda> celdas;
	private int indice_limite_stacking;
	
	public RangoReglaPlanificacionPatio(int rango_regla_planificacion_patio_id, int regla_planificacion_patio_id,
			Integer patio_id, Integer bloque_id, Boolean activo) {
		this.rango_regla_planificacion_patio_id = rango_regla_planificacion_patio_id;
		this.regla_planificacion_patio_id = regla_planificacion_patio_id;
		this.patio_id = patio_id;
		this.bloque_id = bloque_id;
		this.activo = activo;
	}
}
