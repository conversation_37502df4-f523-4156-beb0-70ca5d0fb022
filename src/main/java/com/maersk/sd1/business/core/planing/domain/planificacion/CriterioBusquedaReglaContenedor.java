package com.maersk.sd1.business.core.planing.domain.planificacion;

import java.util.Collection;
import java.util.stream.Collectors;

import com.maersk.sd1.business.core.planing.domain.Contenedor;
import com.maersk.sd1.business.core.planing.domain.Operacion;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CriterioBusquedaReglaContenedor {

	private int patio_id;
	private Integer codigo_grupo_id;
	private Integer cliente_id;
	private Integer linea_naviera_id;
	private String viaje_id;
	private Integer nave_id;
	private String numero_documento_referencia;
	private Integer puerto_descarga_id;
	
	private Integer cat_movimiento_id;
	
	private Integer familia_contenedor_id;	
	private Integer tamano_contenedor_id;
	private Integer tipo_contenedor_id;	
	private Integer clase_contenedor_id;
	
	private Integer condicion_reefer_id;
	private Integer condicion_caja_id;
	private Integer condicion_ca_id;
	
	private Integer tipo_categoria_id;
	private Integer cat_lavado_id;
	private Boolean requiere_conexion_electrica;
	private Integer cat_tecnologia_id;
	private Integer cat_actividad_contenedor_id;
	private Boolean carga_imo;
	private Integer cat_imo_id;

	private Integer cat_structure_condition_id;
	private Integer cat_machine_condition_id;
	private Integer cat_type_activity_id;
	
	private Integer cat_empty_full_id; 
	
	//private String tipo_operacion_codigo;
	//private Boolean vacio;
	//private Integer estado_estimado_box_id;
	//private Integer estado_estimado_reefer_id;

	private Contenedor contenedor;
	private int contenedor_id;

	private Boolean reentrega;
	private Boolean para_venta;

	public CriterioBusquedaReglaContenedor() {
		//this.movimientos_pendientes = new ArrayList<InstruccionMovimiento>();
	}

	public CriterioBusquedaReglaContenedor(String equipmentNumber, Integer contenedor_id) {
		contenedor = new Contenedor(equipmentNumber);
		if (contenedor_id != null)
			contenedor.setContenedor_id(contenedor_id);
		//this.movimientos_pendientes = new ArrayList<InstruccionMovimiento>();
	}

	public static CriterioBusquedaReglaContenedor crear(
			int deposito_id, 
			Integer linea_naviera_id,
			Integer tipo_actividad_contenedor_id, 
			
			Integer tipo_movimiento_id, 
			
			Integer tamano_contenedor_id,
			String tipo_operacion_codigo, 
			Integer cliente_id, 
			String viaje_id, 
			Integer nave_id,
			String numero_documento_referencia, 
			int contenedor_id, 
			Integer codigo_grupo_id,
			
			Integer familia_contenedor_id, 
			Integer clase_contenedor_id, 
			Integer tipo_contenedor_id,
			Integer cat_empty_full_id, 
			Integer cat_structure_condition_id,
			Integer cat_type_activity_id
			) {

		var nuevo = new CriterioBusquedaReglaContenedor();
		nuevo.patio_id = deposito_id;		
		nuevo.linea_naviera_id = linea_naviera_id;		
		nuevo.cat_actividad_contenedor_id = tipo_actividad_contenedor_id;		
		nuevo.cat_movimiento_id = tipo_movimiento_id;		
		nuevo.tamano_contenedor_id = tamano_contenedor_id;
		nuevo.tipo_contenedor_id=tipo_contenedor_id;
		nuevo.cliente_id = cliente_id;
		nuevo.viaje_id = viaje_id;
		nuevo.nave_id = nave_id;
		nuevo.numero_documento_referencia = numero_documento_referencia;
		nuevo.contenedor_id = contenedor_id;
		nuevo.codigo_grupo_id = codigo_grupo_id; 
		nuevo.familia_contenedor_id = familia_contenedor_id;
		nuevo.cat_empty_full_id = cat_empty_full_id;
		nuevo.cat_structure_condition_id = cat_structure_condition_id;
		nuevo.cat_type_activity_id = cat_type_activity_id;
		
		return nuevo;
	}

	public static Collection<CriterioBusquedaReglaContenedor> crear(DatosPlanificacion datos) {
		var criterios = datos.getEquipmentPlanificationData()
			.stream()
			.map(equipmentPlanificationData -> {
					Operacion operation = equipmentPlanificationData.getOperacion();
					Contenedor equipment = equipmentPlanificationData.getContenedor();
					
					if(operation != null) {
						
						CriterioBusquedaReglaContenedor criterio = CriterioBusquedaReglaContenedor
							.crear(
								datos.getPatio_id(),
								operation.getLinea_naviera().getId(),
								equipment.getUltimo_tipo_actividad_id(),
								datos.getTipo_movimiento() != null ? datos.getTipo_movimiento().getId() : null, 
								equipment.getTamanio_contenedor().getId(),
								operation.getTipo_operacion().getCodigo(), 
								operation.getCliente().getId(), 
								operation.getViaje(),
								operation.getNave().getId(), 
								operation.getDocumento_ingreso().getNumero(),
								equipment.getContenedor_id(), 
								operation.getCodigo_grupo_id(),
								
								equipment.getCat_familia_id(),								
								equipment.getCat_clase_id(),
								equipment.getCat_tipo_contenedor_id(),
								operation.getTipo_empty_full().getId(),
								operation.getType_structure_condition() != null ? operation.getType_structure_condition().getId() : null,
								equipment.getUltimo_tipo_actividad_id());
						
						criterio.setContenedor(equipment);

						return criterio;
					}
					return new CriterioBusquedaReglaContenedor();
				}).collect(Collectors.toList());

		return criterios;
	}

}
