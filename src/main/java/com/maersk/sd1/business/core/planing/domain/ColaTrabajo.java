package com.maersk.sd1.business.core.planing.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ColaTrabajo {
	private int cola_trabajo_id;
	private String codigo;
	private String descripcion;
	private boolean activo;
	private boolean por_defecto;
	
	private int patio_id;
	private Integer punto_trabajo_id;
	private Integer usuario_id;
	
	private int estado_id;
	private int estado_padre_id;
	private String estado_codigo;
	private String estado_descripcion;
	
	public ColaTrabajo(Integer cola_trabajo_id, String codigo, String descripcion,
			Boolean activo, Boolean por_defecto, Integer patio_id) {
		this.cola_trabajo_id = cola_trabajo_id;
		this.codigo = codigo;
		this.descripcion = descripcion;
		this.activo = activo;
		this.por_defecto = por_defecto;
		this.patio_id = patio_id;
	}
	
}
