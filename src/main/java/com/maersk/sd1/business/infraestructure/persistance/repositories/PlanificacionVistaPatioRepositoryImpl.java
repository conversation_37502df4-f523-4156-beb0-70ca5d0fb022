package com.maersk.sd1.business.infraestructure.persistance.repositories;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Repository;

import com.maersk.sd1.business.core.planing.port.PlanificacionVistaPatioRepository;
import com.maersk.sd1.business.infraestructure.dto.planificacion.InstruccionMovimientoIngresoDTO;
import com.maersk.sd1.business.infraestructure.dto.planificacion.InstruccionMovimientoSalidaDTO;
import com.maersk.sd1.business.infraestructure.dto.planificacion.PlanificacionBloqueDto;
import com.maersk.sd1.business.infraestructure.dto.planificacion.PlanificacionCeldaDto;
import com.maersk.sd1.business.infraestructure.dto.planificacion.PlanificacionNivelDto;
import com.maersk.sd1.business.infraestructure.dto.planificacion.PlanificacionPatioDto;
import com.maersk.sd1.business.infraestructure.dto.planificacion.PlanificacionPlanoDto;
import com.maersk.sd1.business.infraestructure.shared.utils.GsonUtil;

import lombok.RequiredArgsConstructor;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.Procedure;

@Repository
@RequiredArgsConstructor
public class PlanificacionVistaPatioRepositoryImpl implements PlanificacionVistaPatioRepository {

	@Autowired
	private ApplicationContext context;

	@Override
	public PlanificacionPlanoDto VistaPlanoListar(String unidad_negocio_id) throws SQLException {
		PlanificacionPlanoDto plano = new PlanificacionPlanoDto();

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_plano_patio_bloque_listar");
		try {
			pResult.input("unidad_negocio_id", unidad_negocio_id, Jpo.INTEGER);
			List<Object> resultado = (List<Object>) pResult.execute();

			plano = GsonUtil.GetModel(resultado.get(0), PlanificacionPlanoDto.class);
			List<PlanificacionPatioDto> patios = GsonUtil.GetList(resultado.get(1), PlanificacionPatioDto.class);
			List<PlanificacionBloqueDto> bloques = GsonUtil.GetList(resultado.get(2), PlanificacionBloqueDto.class);

			patios.forEach(patio -> {
				var bloquesFiltrados = bloques.stream().filter(bloque -> bloque.getPatio_id() == patio.getPatio_id())
						.collect(Collectors.toList());
				patio.setBloques(bloquesFiltrados);
			});

			plano.setPatios(patios);

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return plano;
	}

	@Override
	public List<PlanificacionCeldaDto> VistaCeldaPorBloqueListar(String bloque_id) throws SQLException {
		List<PlanificacionCeldaDto> celdas = new ArrayList<PlanificacionCeldaDto>();

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_celda_por_bloque_listar");
		try {
			pResult.input("bloque_id", bloque_id, Jpo.STRING);
			List<Object> resultado = (List<Object>) pResult.execute();
			celdas = GsonUtil.GetList(resultado, PlanificacionCeldaDto.class);

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return celdas;
	}

	@Override
	public List<PlanificacionNivelDto> VistaNivelConContenedorListar(String json_celdas) throws SQLException {
		List<PlanificacionNivelDto> niveles = new ArrayList<PlanificacionNivelDto>();

		var conn = context.getBean(Jpo.class);
		Procedure pResult = conn.procedure("sdy.planificacion_nivel_con_contendor_listar");
		try {
			pResult.input("json_celdas", json_celdas, Jpo.STRING);
			System.out.println(json_celdas);
			List<Object> resultado = (List<Object>) pResult.execute();

			niveles = GsonUtil.GetList(resultado.get(0), PlanificacionNivelDto.class);

			List<InstruccionMovimientoIngresoDTO> instruccionesMovimientoIngreso = GsonUtil.GetList(resultado.get(1),
					InstruccionMovimientoIngresoDTO.class);

			for (var imo : instruccionesMovimientoIngreso) {
				var nivel = niveles.stream().filter(im -> im.getCelda_id() == imo.getDestino_celda_id() && im.getIndice() == imo.getIndice()).findFirst()
						.orElse(null);
				if (nivel != null) {
					nivel.setInstruccionMovimientoIngreso(imo);
				}
			}

			List<InstruccionMovimientoSalidaDTO> instruccionesMovimientoSalida = GsonUtil.GetList(resultado.get(2),
					InstruccionMovimientoSalidaDTO.class);
			
			for (var imo : instruccionesMovimientoSalida) {
				var nivel = niveles.stream().filter(im -> im.getContenedor_id() != null && im.getContenedor_id() == imo.getContenedor_id()).findFirst()
						.orElse(null);
				if (nivel != null) {
					nivel.setInstruccionMovimientoSalida(imo);
				}
			}

			conn.commit();
		} catch (Exception e) {
			conn.finalizar();
			e.printStackTrace();
		}
		return niveles;
	}

}
