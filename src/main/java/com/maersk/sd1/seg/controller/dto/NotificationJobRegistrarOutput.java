package com.maersk.sd1.seg.controller.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Builder
@Data
public class NotificationJobRegistrarOutput<T> {

    private int id;
    private int statusCode;
    private String message;

    private List<T> result;

    public List<T> toResultArray() {
        return (List<T>) Arrays.asList(id, statusCode, message);
    }
}