package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.NotificationJobProcessListOutput;
import com.maersk.sd1.seg.service.NotificationJobProcessListService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSEG/module/seg/SEGNotificationJobProcessListServiceImp")
@RequiredArgsConstructor
public class NotificationJobProcessListController {
    private static final Logger logger = LogManager.getLogger(NotificationJobProcessListController.class.getName());

    private final NotificationJobProcessListService service;

    @PostMapping("/segNotificationJobProcessList")
    public ResponseEntity<ResponseController<List<NotificationJobProcessListOutput>>> notificationJobProcessList() {
        try {
            List<NotificationJobProcessListOutput> result = service.processNotificationJobList();
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request."));
        }
    }
}