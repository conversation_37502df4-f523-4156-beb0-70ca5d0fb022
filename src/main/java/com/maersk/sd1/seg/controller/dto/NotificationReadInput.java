package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class NotificationReadInput {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Input {
        @JsonProperty("tipo")
        private Integer type;

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("notificacion_id")
        private Integer notificationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix seg;

        public Input getInput() {
            return seg != null ? seg.getInput() : null;
        }
    }

}