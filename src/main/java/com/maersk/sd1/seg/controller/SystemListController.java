package com.maersk.sd1.seg.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.SystemsListInput;
import com.maersk.sd1.seg.dto.SystemsListOutput;
import com.maersk.sd1.seg.service.SystemListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller to expose the endpoint replicating the stored procedure logic.
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/ADMSistemaService")
public class SystemListController {

    private static final Logger logger = LogManager.getLogger(SystemListController.class.getName());

    private final SystemListService systemListService;

    @PostMapping("/segsistemaListar")
    public ResponseEntity<ResponseController<SystemsListOutput>> listSystems(
            @RequestBody @Valid SystemsListInput.Root request) {
        try {
            logger.info("Request received for listing systems: {}", request);
            SystemsListInput.Input input = request.getPrefix().getInput();
            SystemsListOutput output = systemListService.listSystems(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing systems.", e);
            SystemsListOutput errorOutput = new SystemsListOutput();
            // we can optionally set totalRecords or systems fields to indicate an error.
            // For simplicity, we just return an empty list.
            errorOutput.setTotalRecords(0L);
            errorOutput.setSystems(null);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
