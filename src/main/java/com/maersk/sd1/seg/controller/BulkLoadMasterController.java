package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.BulkLoadMasterOutputDTO;
import com.maersk.sd1.seg.service.BulkLoadMasterService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.maersk.sd1.seg.dto.BulkLoadMasterInputDTO;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/ADMCargaService")
public class BulkLoadMasterController {

    private final BulkLoadMasterService bulkLoadMasterService;

    @PostMapping("/segcargaMasivaMaster")
    public ResponseEntity<BulkLoadMasterOutputDTO> executeCargaMasiva(
            @RequestBody BulkLoadMasterInputDTO.Root requestDTO) {
        BulkLoadMasterOutputDTO response = bulkLoadMasterService.executeBulkLoad(requestDTO);
        return ResponseEntity.ok(response);
    }
}


