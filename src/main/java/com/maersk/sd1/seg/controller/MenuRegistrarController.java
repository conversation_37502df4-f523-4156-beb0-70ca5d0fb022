package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.*;
import com.maersk.sd1.seg.service.MenuRegistrarService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.maersk.sd1.common.Constants.ERROR_MSG;

@RestController
@RequestMapping("/ModuleADM/module/adm/seguridad/ADMMenuServiceImp")
public class MenuRegistrarController {

    private final MenuRegistrarService menuRegistrarService;

    @Autowired
    public MenuRegistrarController(MenuRegistrarService menuRegistrarService)
    {
        this.menuRegistrarService = menuRegistrarService;
    }

    @PostMapping("/segmenuRegistrar")
    public ResponseEntity<ResponseController<MenuRegistrarOutput>> menuRegistrar(
            @RequestBody MenuRegistrarInput.Root input) {

        if (input.getInput().getMenuBaseId() == null || input.getInput().getParentMenuId() == null
                || input.getInput().getTitle() == null || input.getInput().getTemplate() == null ||
                input.getInput().getIcon() == null || input.getInput().getOrder() == null ||
                input.getInput().getStatus() == null || input.getInput().getUserId() == null ||
                input.getInput().getConfiguration() == null || input.getInput().getRolesId() == null ||
                input.getInput().getActions() == null) {
            return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
        }

        try {
            MenuRegistrarOutput output = menuRegistrarService.menuRegistrar(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            MenuRegistrarOutput result = new MenuRegistrarOutput();
            result.setRespNewId(0);
            result.setRespEstado(0);
            result.setRespMensaje(ERROR_MSG);
            return ResponseEntity.internalServerError().body(new ResponseController<>(result));
        }


    }

    @PostMapping("/segmenuListarProyectos")
    public ResponseEntity<ResponseController<List<MenuProjectListOutput>>> retrieveProjectMenuList() {
        List<MenuProjectListOutput> menuProjectListOutput = menuRegistrarService.retrieveProjectMenuList();
        return ResponseEntity.ok(new ResponseController<>(menuProjectListOutput));
    }


    @PostMapping("/segmenuObtener")
    public ResponseEntity<ResponseController<MenuDetailsDTO>> getMenuDetails(@RequestBody RetrieveMenuInput.Root input) {
        if (input.getInput().getMenu_id() ==  null)
        {
            return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
        }
        MenuDetailsDTO retrieveMenuOutputDTO = menuRegistrarService.getMenuDetails(input.getInput().getMenu_id());
        return ResponseEntity.ok(new ResponseController<>(retrieveMenuOutputDTO));
    }

}




