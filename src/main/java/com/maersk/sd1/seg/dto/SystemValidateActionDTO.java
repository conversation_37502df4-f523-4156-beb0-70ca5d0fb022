package com.maersk.sd1.seg.dto;

import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.NotificationActionTemplate;
import com.maersk.sd1.common.model.NotificationTemplate;

public interface SystemValidateActionDTO {

    NotificationActionTemplate getNotificationActionClass();
    NotificationTemplate getNotificationTemplateClass();
    Menu getMenuClass();
    Integer getRead();

}