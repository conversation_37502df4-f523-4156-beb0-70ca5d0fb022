package com.maersk.sd1.sdg.repository;

import com.maersk.sd1.common.repository.ChassisRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SdgChassisRepository extends ChassisRepository {


    @Query(value = "SELECT C.id FROM Chassis C WHERE C.id <> :chassisId AND C.chassisNumber = :chassisNumber")
    Integer findChassisId(@Param("chassisId") Integer chassisId, @Param("chassisNumber") String chassisNumber);

}
