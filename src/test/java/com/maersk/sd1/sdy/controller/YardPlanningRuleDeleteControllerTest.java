package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.YardPlanningRuleDeleteInput;
import com.maersk.sd1.sdy.dto.YardPlanningRuleDeleteOutput;
import com.maersk.sd1.sdy.service.YardPlanningRuleDeleteService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YardPlanningRuleDeleteControllerTest {

    @Mock
    private YardPlanningRuleDeleteService yardPlanningRuleDeleteService;

    @InjectMocks
    private YardPlanningRuleDeleteController yardPlanningRuleDeleteController;

    @Test
    void Given_ValidRequest_When_DeactivateYardPlanningRule_Then_ReturnSuccessResponse() {
        // Given
        YardPlanningRuleDeleteInput.Root request = new YardPlanningRuleDeleteInput.Root();
        YardPlanningRuleDeleteInput.Prefix prefix = new YardPlanningRuleDeleteInput.Prefix();
        YardPlanningRuleDeleteInput.Input input = new YardPlanningRuleDeleteInput.Input();
        input.setYardPlanningRuleId(1);
        input.setUserModificationId(2);
        input.setLanguageId(3);
        prefix.setInput(input);
        request.setPrefix(prefix);

        YardPlanningRuleDeleteOutput expectedOutput = new YardPlanningRuleDeleteOutput();
        expectedOutput.setRespEstado(1);
        expectedOutput.setRespMensaje("Success");

        when(yardPlanningRuleDeleteService.deactivateYardPlanningRule(1, 2, 3)).thenReturn(expectedOutput);

        // When
        ResponseEntity<ResponseController<YardPlanningRuleDeleteOutput>> response = yardPlanningRuleDeleteController.deactivateYardPlanningRule(request);

        // Then
        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
        verify(yardPlanningRuleDeleteService, times(1)).deactivateYardPlanningRule(1, 2, 3);
    }

    @Test
    void Given_ExceptionThrown_When_DeactivateYardPlanningRule_Then_ReturnErrorResponse() {
        // Given
        YardPlanningRuleDeleteInput.Root request = new YardPlanningRuleDeleteInput.Root();
        YardPlanningRuleDeleteInput.Prefix prefix = new YardPlanningRuleDeleteInput.Prefix();
        YardPlanningRuleDeleteInput.Input input = new YardPlanningRuleDeleteInput.Input();
        input.setYardPlanningRuleId(1);
        input.setUserModificationId(2);
        input.setLanguageId(3);
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(yardPlanningRuleDeleteService.deactivateYardPlanningRule(1, 2, 3)).thenThrow(new RuntimeException("Test Exception"));

        // When
        ResponseEntity<ResponseController<YardPlanningRuleDeleteOutput>> response = yardPlanningRuleDeleteController.deactivateYardPlanningRule(request);

        // Then
        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Test Exception", response.getBody().getResult().getRespMensaje());
        verify(yardPlanningRuleDeleteService, times(1)).deactivateYardPlanningRule(1, 2, 3);
    }
}