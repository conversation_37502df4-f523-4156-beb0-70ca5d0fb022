package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.BlockRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sdy.dto.BlockNoVirtualByYardOutput;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BlockNoVirtualByYardServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private BlockRepository blockRepository;

    @InjectMocks
    private BlockNoVirtualByYardService blockNoVirtualByYardService;

    @Test
    void Given_ValidYardId_When_CatalogAndBlocksExist_Then_ReturnBlockDataList() {
        // Arrange
        Integer yardId = 1;
        Catalog virtualBlockCatalog = new Catalog();
        virtualBlockCatalog.setId(100);
        when(catalogRepository.findByAlias("sd1_block_type_virtual")).thenReturn(virtualBlockCatalog);

        Block block = new Block();
        block.setId(1);
        block.setName("Block A");
        block.setCode("A1");
        when(blockRepository.findByYardIdAndCatBlockTypeIdNotAndActiveTrue(yardId, 100))
                .thenReturn(List.of(block));

        // Act
        BlockNoVirtualByYardOutput result = blockNoVirtualByYardService.getBlocksNonVirtualByYard(yardId);

        // Assert
        assertEquals(1, result.getBlocks().size());
        assertEquals(1, result.getBlocks().get(0).getBlockId());
        assertEquals("Block A", result.getBlocks().get(0).getName());
        assertEquals("A1", result.getBlocks().get(0).getCode());
        verify(catalogRepository, times(1)).findByAlias("sd1_block_type_virtual");
        verify(blockRepository, times(1)).findByYardIdAndCatBlockTypeIdNotAndActiveTrue(yardId, 100);
    }

    @Test
    void Given_ValidYardId_When_CatalogDoesNotExist_Then_ReturnNullBlocks() {
        // Arrange
        Integer yardId = 1;
        when(catalogRepository.findByAlias("sd1_block_type_virtual")).thenReturn(null);

        // Act
        BlockNoVirtualByYardOutput result = blockNoVirtualByYardService.getBlocksNonVirtualByYard(yardId);

        // Assert
        assertEquals(null, result.getBlocks());
        verify(catalogRepository, times(1)).findByAlias("sd1_block_type_virtual");
        verify(blockRepository, never()).findByYardIdAndCatBlockTypeIdNotAndActiveTrue(anyInt(), anyInt());
    }

    @Test
    void Given_ValidYardId_When_NoBlocksExist_Then_ReturnEmptyBlockList() {
        // Arrange
        Integer yardId = 1;
        Catalog virtualBlockCatalog = new Catalog();
        virtualBlockCatalog.setId(100);
        when(catalogRepository.findByAlias("sd1_block_type_virtual")).thenReturn(virtualBlockCatalog);

        when(blockRepository.findByYardIdAndCatBlockTypeIdNotAndActiveTrue(yardId, 100))
                .thenReturn(Collections.emptyList());

        // Act
        BlockNoVirtualByYardOutput result = blockNoVirtualByYardService.getBlocksNonVirtualByYard(yardId);

        // Assert
        assertEquals(0, result.getBlocks().size());
        verify(catalogRepository, times(1)).findByAlias("sd1_block_type_virtual");
        verify(blockRepository, times(1)).findByYardIdAndCatBlockTypeIdNotAndActiveTrue(yardId, 100);
    }

    @Test
    void Given_ValidYardId_When_ExceptionOccurs_Then_ReturnNullBlocks() {
        // Arrange
        Integer yardId = 1;
        when(catalogRepository.findByAlias("sd1_block_type_virtual")).thenThrow(new RuntimeException("Database error"));

        // Act
        BlockNoVirtualByYardOutput result = blockNoVirtualByYardService.getBlocksNonVirtualByYard(yardId);

        // Assert
        assertEquals(null, result.getBlocks());
        verify(catalogRepository, times(1)).findByAlias("sd1_block_type_virtual");
        verify(blockRepository, never()).findByYardIdAndCatBlockTypeIdNotAndActiveTrue(anyInt(), anyInt());
    }
}