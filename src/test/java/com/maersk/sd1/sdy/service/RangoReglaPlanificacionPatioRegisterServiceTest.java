package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.RangoReglaPlanificacionPatioRegisterInputDTO;
import com.maersk.sd1.sdy.dto.RangoReglaPlanificacionPatioRegisterOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RangoReglaPlanificacionPatioRegisterServiceTest {

    @Mock
    private UserRepository userRepository;
    @Mock
    private YardPlanningRuleRangeRepository yardPlanningRuleRangeRepository;
    @Mock
    private YardPlanningRuleRepository yardPlanningRuleRepository;
    @Mock
    private BlockRepository blockRepository;
    @Mock
    private BlockRestrictionRepository blockRestrictionRepository;
    @Mock
    private ContainerLocationRepository containerLocationRepository;
    @Mock
    private StockEmptyRepository stockEmptyRepository;
    @Mock
    private StockFullRepository stockFullRepository;
    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private RangoReglaPlanificacionPatioRegisterService service;

    private RangoReglaPlanificacionPatioRegisterInputDTO.Input validInput;
    private YardPlanningRule mockYardPlanningRule;
    private Block mockBlock;
    private User mockUser;

    @BeforeEach
    void setUp() {
        // Setup valid input
        validInput = new RangoReglaPlanificacionPatioRegisterInputDTO.Input();
        validInput.setReglaPlanificacionPatioId(1);
        validInput.setBloqueId(1);
        validInput.setIndiceFilaDesde(1);
        validInput.setIndiceFilaHasta(5);
        validInput.setIndiceColumnaDesde(1);
        validInput.setIndiceColumnaHasta(5);
        validInput.setActivo(true);
        validInput.setUsuarioRegistroId(1);
        validInput.setIdiomaId(1);
        validInput.setDireccionStacking(true);
        validInput.setIndiceLimiteStacking(3);

        // Setup mock objects
        mockYardPlanningRule = new YardPlanningRule();
        mockYardPlanningRule.setId(1);
        Catalog mockCatSize = new Catalog();
        mockCatSize.setId(20);
        mockYardPlanningRule.setCatContainerSize(mockCatSize);
        Catalog mockCatEmptyFull = new Catalog();
        mockCatEmptyFull.setId(10);
        mockYardPlanningRule.setCatEmptyFull(mockCatEmptyFull);

        mockBlock = new Block();
        mockBlock.setId(1);
        mockBlock.setLevels(5);

        mockUser = new User();
        mockUser.setId(1);
    }

    @Test
    void Given_ValidInput_When_RegisterRange_Then_ReturnSuccessResponse() {
        // Arrange
        when(yardPlanningRuleRepository.findById(1)).thenReturn(Optional.of(mockYardPlanningRule));
        when(yardPlanningRuleRangeRepository.findConflictingRangeDifferentTypeOrSize(
                anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt()
        )).thenReturn(new ArrayList<>());
        when(blockRepository.findById(1)).thenReturn(Optional.of(mockBlock));
        when(blockRestrictionRepository.findByBlockIdAndActiveTrue(1)).thenReturn(new ArrayList<>());
        when(userRepository.findById(1)).thenReturn(Optional.of(mockUser));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, 1)).thenReturn("Success");

        YardPlanningRuleRange savedRange = new YardPlanningRuleRange();
        savedRange.setId(100);
        when(yardPlanningRuleRangeRepository.save(any(YardPlanningRuleRange.class))).thenReturn(savedRange);

        // Act
        RangoReglaPlanificacionPatioRegisterOutputDTO result = service.registerRange(validInput);

        // Assert
        assertEquals(1, result.getRespEstado());
        assertEquals("Success", result.getRespMensaje());
        assertEquals(100, result.getRespNewId());
        verify(yardPlanningRuleRangeRepository).save(any(YardPlanningRuleRange.class));
    }

    @Test
    void Given_ConflictingRange_When_RegisterRange_Then_ReturnErrorResponse() {
        // Arrange
        when(yardPlanningRuleRepository.findById(1)).thenReturn(Optional.of(mockYardPlanningRule));
        List<String> conflicts = List.of("Range 1");
        when(yardPlanningRuleRangeRepository.findConflictingRangeDifferentTypeOrSize(
                anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt()
        )).thenReturn(conflicts);

        // Act
        RangoReglaPlanificacionPatioRegisterOutputDTO result = service.registerRange(validInput);

        // Assert
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("There is already a range assigned"));
        verify(yardPlanningRuleRangeRepository, never()).save(any(YardPlanningRuleRange.class));
    }

    @Test
    void Given_InvalidYardPlanningRuleId_When_RegisterRange_Then_ThrowException() {
        // Arrange
        when(yardPlanningRuleRepository.findById(1)).thenReturn(Optional.empty());

        // Act & Assert
        RangoReglaPlanificacionPatioRegisterOutputDTO result = service.registerRange(validInput);
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Invalid regla_planificacion_patio_id"));
    }
}