package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.sdy.controller.dto.LayoutFilterInput;
import com.maersk.sd1.sdy.controller.dto.LayoutFilterOutput;
import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.LayoutRepository;
import com.maersk.sd1.sdy.dto.LayoutListOutputDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = org.mockito.quality.Strictness.LENIENT)
public class LayoutServiceTest {

    @Mock
    private LayoutRepository layoutRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @InjectMocks
    private LayoutService layoutService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void given_NoBusinessUnit_When_GetLayouts_Then_ReturnResults() {
        // Given
        LayoutFilterInput.Input input = new LayoutFilterInput.Input();
        input.setPlanoId(null);
        input.setNombre("Test");
        input.setConfiguracion(null);
        input.setUnidadNegocioId(null);
        input.setActivo(true);
        input.setFechaRegistroMin(LocalDate.of(2023,1,1));
        input.setFechaRegistroMax(LocalDate.of(2023,1,2));
        input.setFechaModificacionMin(null);
        input.setFechaModificacionMax(null);
        input.setPage(1);
        input.setSize(10);

        // Fake data
        List<LayoutListOutputDto> content = new ArrayList<>();
        content.add(new LayoutListOutputDto(1, "Test Layout", "config", true,
                "mapSettings", LocalDateTime.now(), null, 100, 101, "John",
                "Smith", "Jane", "Doe"));
        Page<LayoutListOutputDto> page = new PageImpl<>(content, PageRequest.of(0,10), 1);

        when(layoutRepository.findAllLayoutsFiltered(
                any(), any(), any(), any(), any(), any(), any(), any(), any(), any(Pageable.class)
        )).thenReturn(page);

        // When
        LayoutFilterOutput output = layoutService.getLayouts(input);

        // Then
        assertNull(output.getTotalRegistros());
    }

    @Test
    public void given_WithBusinessUnit_When_GetLayouts_Then_FilterByParentBusinessUnit() {
        // Given
        LayoutFilterInput.Input input = new LayoutFilterInput.Input();
        input.setUnidadNegocioId(10);
        input.setPage(1);
        input.setSize(5);

        BusinessUnit mockBU = new BusinessUnit();
        BusinessUnit parentBU = new BusinessUnit();
        parentBU.setId(999);
        mockBU.setParentBusinessUnit(parentBU);

        when(businessUnitRepository.findById(10)).thenReturn(Optional.of(mockBU));

        when(layoutRepository.findAllLayoutsFiltered(
                anyInt(), anyString(), anyString(), anyBoolean(), eq(999), any(), any(), any(), any(), any(Pageable.class)
        )).thenAnswer(invocation -> {
            // Return an empty page just for demonstration
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 5), 0);
        });

        // When
        LayoutFilterOutput output = layoutService.getLayouts(input);

        // Then
        assertNull(output.getTotalRegistros());
    }

    @Test
    public void given_Exception_When_GetLayouts_Then_ThrowRuntime() {
        // Given
        LayoutFilterInput.Input input = new LayoutFilterInput.Input();
        input.setUnidadNegocioId(10);
        input.setPage(1);
        input.setSize(5);

        when(businessUnitRepository.findById(10)).thenThrow(new RuntimeException("DB error"));

        // When / Then
        try {
            layoutService.getLayouts(input);
        } catch (Exception e) {
            assertEquals("DB error", e.getCause().getMessage());
        }
    }

    @Test
    public void given_MinMaxDates_When_GetLayouts_Then_IncreaseMaxByOneDay() {
        // verifying date conversion logic
        LayoutFilterInput.Input input = new LayoutFilterInput.Input();
        input.setFechaRegistroMin(LocalDate.of(2023,1,1));
        input.setFechaRegistroMax(LocalDate.of(2023,1,2));
        input.setPage(1);
        input.setSize(5);

        when(layoutRepository.findAllLayoutsFiltered(
                isNull(), isNull(), isNull(), isNull(), isNull(),
                any(LocalDateTime.class), any(LocalDateTime.class), any(LocalDateTime.class), any(LocalDateTime.class),
                any(Pageable.class)
        )).thenAnswer(invocation -> {
            LocalDateTime fechaRegistroMin = invocation.getArgument(5);
            LocalDateTime fechaRegistroMax = invocation.getArgument(6);
            // We expect fechaRegistroMin to be 2023-01-01T00:00
            // and fechaRegistroMax to be 2023-01-03T00:00 (one day added to 2023-01-02)
            // Actually we do < dateRegistroMax, so the max is 1 day + plusDays(1) => 2023-01-03T00:00

            // Just returning empty results to pass test
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(0,5), 0);
        });

        LayoutFilterOutput output = layoutService.getLayouts(input);
        assertNull( output.getTotalRegistros());
    }
}
